#!/usr/bin/env python3
"""
Pentest Database Manager
Sistema di database per tracciare tutte le task e risultati di penetration testing
"""

import sqlite3
import json
import logging
import os
from datetime import datetime
from typing import Dict, Any, List, Optional
from contextlib import contextmanager


class PentestDatabase:
    """Manager per il database delle attività di penetration testing"""
    
    def __init__(self, db_path: str = "database/pentest.db"):
        self.db_path = db_path
        self.logger = logging.getLogger("heka.pentest_database")
        
        # Crea directory se non esiste
        os.makedirs(os.path.dirname(db_path), exist_ok=True)
        
        # Inizializza database
        self._initialize_database()
    
    def _initialize_database(self):
        """Inizializza le tabelle del database"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Tabella targets
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS targets (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    target_name TEXT UNIQUE NOT NULL,
                    target_type TEXT NOT NULL,  -- ip, domain, range
                    description TEXT,
                    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
                )
            """)
            
            # Tabella pentest_sessions
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS pentest_sessions (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    target_id INTEGER NOT NULL,
                    session_name TEXT NOT NULL,
                    start_time TIMESTAMP NOT NULL,
                    end_time TIMESTAMP,
                    status TEXT NOT NULL,  -- running, completed, failed, cancelled
                    objective TEXT,
                    final_report_path TEXT,
                    FOREIGN KEY (target_id) REFERENCES targets (id)
                )
            """)
            
            # Tabella agent_tasks
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS agent_tasks (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id INTEGER NOT NULL,
                    agent_name TEXT NOT NULL,
                    task_type TEXT NOT NULL,
                    task_description TEXT,
                    command_executed TEXT,
                    start_time TIMESTAMP NOT NULL,
                    end_time TIMESTAMP,
                    status TEXT NOT NULL,  -- running, completed, failed
                    result_summary TEXT,
                    raw_output TEXT,
                    vulnerabilities_found INTEGER DEFAULT 0,
                    FOREIGN KEY (session_id) REFERENCES pentest_sessions (id)
                )
            """)
            
            # Tabella vulnerabilities
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS vulnerabilities (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id INTEGER NOT NULL,
                    task_id INTEGER,
                    vulnerability_type TEXT NOT NULL,
                    severity TEXT NOT NULL,  -- critical, high, medium, low, info
                    title TEXT NOT NULL,
                    description TEXT,
                    affected_service TEXT,
                    port INTEGER,
                    proof_of_concept TEXT,
                    remediation TEXT,
                    cvss_score REAL,
                    cve_id TEXT,
                    discovered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (session_id) REFERENCES pentest_sessions (id),
                    FOREIGN KEY (task_id) REFERENCES agent_tasks (id)
                )
            """)
            
            # Tabella agent_communications
            cursor.execute("""
                CREATE TABLE IF NOT EXISTS agent_communications (
                    id INTEGER PRIMARY KEY AUTOINCREMENT,
                    session_id INTEGER NOT NULL,
                    from_agent TEXT NOT NULL,
                    to_agent TEXT NOT NULL,
                    message_type TEXT NOT NULL,  -- request, response, info, alert
                    message_content TEXT NOT NULL,
                    timestamp TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
                    FOREIGN KEY (session_id) REFERENCES pentest_sessions (id)
                )
            """)
            
            conn.commit()
            self.logger.info("Database inizializzato con successo")
    
    @contextmanager
    def _get_connection(self):
        """Context manager per connessioni database"""
        conn = sqlite3.connect(self.db_path)
        conn.row_factory = sqlite3.Row  # Permette accesso per nome colonna
        try:
            yield conn
        finally:
            conn.close()
    
    def create_target(self, target_name: str, target_type: str, description: str = "") -> int:
        """Crea un nuovo target"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT OR REPLACE INTO targets (target_name, target_type, description, updated_at)
                VALUES (?, ?, ?, CURRENT_TIMESTAMP)
            """, (target_name, target_type, description))
            conn.commit()
            return cursor.lastrowid
    
    def create_pentest_session(self, target_id: int, session_name: str, objective: str) -> int:
        """Crea una nuova sessione di penetration testing"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO pentest_sessions 
                (target_id, session_name, start_time, status, objective)
                VALUES (?, ?, CURRENT_TIMESTAMP, 'running', ?)
            """, (target_id, session_name, objective))
            conn.commit()
            return cursor.lastrowid
    
    def create_agent_task(self, session_id: int, agent_name: str, task_type: str, 
                         task_description: str, command_executed: str = "") -> int:
        """Crea una nuova task per un agente"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO agent_tasks 
                (session_id, agent_name, task_type, task_description, command_executed, 
                 start_time, status)
                VALUES (?, ?, ?, ?, ?, CURRENT_TIMESTAMP, 'running')
            """, (session_id, agent_name, task_type, task_description, command_executed))
            conn.commit()
            return cursor.lastrowid
    
    def update_agent_task(self, task_id: int, status: str, result_summary: str = "", 
                         raw_output: str = "", vulnerabilities_found: int = 0):
        """Aggiorna una task dell'agente"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE agent_tasks 
                SET status = ?, result_summary = ?, raw_output = ?, 
                    vulnerabilities_found = ?, end_time = CURRENT_TIMESTAMP
                WHERE id = ?
            """, (status, result_summary, raw_output, vulnerabilities_found, task_id))
            conn.commit()
    
    def create_vulnerability(self, session_id: int, task_id: int, vuln_data: Dict[str, Any]) -> int:
        """Registra una vulnerabilità trovata"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO vulnerabilities 
                (session_id, task_id, vulnerability_type, severity, title, description,
                 affected_service, port, proof_of_concept, remediation, cvss_score, cve_id)
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            """, (
                session_id, task_id,
                vuln_data.get('type', 'unknown'),
                vuln_data.get('severity', 'info'),
                vuln_data.get('title', 'Vulnerability'),
                vuln_data.get('description', ''),
                vuln_data.get('service', ''),
                vuln_data.get('port'),
                vuln_data.get('poc', ''),
                vuln_data.get('remediation', ''),
                vuln_data.get('cvss_score'),
                vuln_data.get('cve_id', '')
            ))
            conn.commit()
            return cursor.lastrowid
    
    def log_agent_communication(self, session_id: int, from_agent: str, to_agent: str,
                               message_type: str, message_content: str):
        """Logga comunicazione tra agenti"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                INSERT INTO agent_communications 
                (session_id, from_agent, to_agent, message_type, message_content)
                VALUES (?, ?, ?, ?, ?)
            """, (session_id, from_agent, to_agent, message_type, message_content))
            conn.commit()
    
    def complete_pentest_session(self, session_id: int, status: str = "completed", 
                                report_path: str = ""):
        """Completa una sessione di penetration testing"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                UPDATE pentest_sessions 
                SET status = ?, end_time = CURRENT_TIMESTAMP, final_report_path = ?
                WHERE id = ?
            """, (status, report_path, session_id))
            conn.commit()
    
    def get_target_by_name(self, target_name: str) -> Optional[Dict[str, Any]]:
        """Recupera target per nome"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM targets WHERE target_name = ?", (target_name,))
            row = cursor.fetchone()
            return dict(row) if row else None
    
    def get_session_tasks(self, session_id: int) -> List[Dict[str, Any]]:
        """Recupera tutte le task di una sessione"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM agent_tasks 
                WHERE session_id = ? 
                ORDER BY start_time
            """, (session_id,))
            return [dict(row) for row in cursor.fetchall()]
    
    def get_session_vulnerabilities(self, session_id: int) -> List[Dict[str, Any]]:
        """Recupera tutte le vulnerabilità di una sessione"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM vulnerabilities 
                WHERE session_id = ? 
                ORDER BY severity DESC, discovered_at
            """, (session_id,))
            return [dict(row) for row in cursor.fetchall()]
    
    def get_session_communications(self, session_id: int) -> List[Dict[str, Any]]:
        """Recupera tutte le comunicazioni di una sessione"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM agent_communications 
                WHERE session_id = ? 
                ORDER BY timestamp
            """, (session_id,))
            return [dict(row) for row in cursor.fetchall()]
    
    def get_session_summary(self, session_id: int) -> Dict[str, Any]:
        """Recupera summary completo di una sessione"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            
            # Info sessione
            cursor.execute("SELECT * FROM pentest_sessions WHERE id = ?", (session_id,))
            session = dict(cursor.fetchone())
            
            # Statistiche task
            cursor.execute("""
                SELECT 
                    COUNT(*) as total_tasks,
                    SUM(CASE WHEN status = 'completed' THEN 1 ELSE 0 END) as completed_tasks,
                    SUM(CASE WHEN status = 'failed' THEN 1 ELSE 0 END) as failed_tasks,
                    SUM(vulnerabilities_found) as total_vulnerabilities_found
                FROM agent_tasks WHERE session_id = ?
            """, (session_id,))
            task_stats = dict(cursor.fetchone())
            
            # Statistiche vulnerabilità per severità
            cursor.execute("""
                SELECT 
                    severity,
                    COUNT(*) as count
                FROM vulnerabilities 
                WHERE session_id = ? 
                GROUP BY severity
            """, (session_id,))
            vuln_stats = {row['severity']: row['count'] for row in cursor.fetchall()}
            
            return {
                'session': session,
                'task_statistics': task_stats,
                'vulnerability_statistics': vuln_stats,
                'tasks': self.get_session_tasks(session_id),
                'vulnerabilities': self.get_session_vulnerabilities(session_id),
                'communications': self.get_session_communications(session_id)
            }
    
    def get_all_targets(self) -> List[Dict[str, Any]]:
        """Recupera tutti i target"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("SELECT * FROM targets ORDER BY created_at DESC")
            return [dict(row) for row in cursor.fetchall()]
    
    def get_target_sessions(self, target_id: int) -> List[Dict[str, Any]]:
        """Recupera tutte le sessioni di un target"""
        with self._get_connection() as conn:
            cursor = conn.cursor()
            cursor.execute("""
                SELECT * FROM pentest_sessions 
                WHERE target_id = ? 
                ORDER BY start_time DESC
            """, (target_id,))
            return [dict(row) for row in cursor.fetchall()]


# Istanza globale del database
pentest_db = PentestDatabase()
