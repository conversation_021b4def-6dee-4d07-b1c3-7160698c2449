# 🛡️ Heka Automated Penetration Testing System

Sistema multi-agent automatizzato per penetration testing completo con database SQL e generazione report PDF.

## 🚀 Caratteristiche Principali

### ✨ Funzionalità Implementate

- **🎯 Input Target Interattivo**: Richiede target (IP, dominio, CIDR) all'avvio
- **🤖 Multi-Agent Orchestration**: Coordina automaticamente tutti gli agenti
- **🔧 Kali Executor Agent**: Esegue comandi nella shell di Kali Linux
- **📊 Database SQL**: Traccia tutte le attività per ogni target
- **📄 Report PDF**: Genera report dettagliati automaticamente
- **🔧 Sistema Completo**: <PERSON><PERSON> tutte le dipendenze per funzionamento completo

### 🏗️ Architettura del Sistema

```
pentest_manager.py          # Manager principale
├── PentestManager          # Classe principale
├── Target Input            # Validazione e input target
├── Automated Workflow      # Orchestrazione fasi
└── Report Generation       # PDF e database

agents/
├── pentest_crew.py         # Coordinamento agenti
├── kali_executor_tool.py   # Esecuzione comandi Kali
├── web_browser_tool.py     # Web intelligence
└── ai_client.py           # Backend LLM

database/
└── pentest_database.py    # Database SQLite

reports/
└── pdf_generator.py       # Generazione PDF
```

## 🎯 Utilizzo

### Avvio Sistema

#### Metodo Raccomandato (con script)
```bash
./start_heka.sh
```

#### Metodo Manuale
```bash
# Assicurati di essere nell'ambiente conda corretto
/media/b0/dev/miniconda3/envs/heka/bin/python3 pentest_manager.py
```

### Flusso di Lavoro

1. **Input Target**: Il sistema richiede di inserire un target
   - Indirizzo IP (es: `*************`)
   - Dominio (es: `example.com`)
   - Range CIDR (es: `***********/24`)

2. **Esecuzione Automatica**: Il sistema esegue automaticamente:
   - 🔍 **Reconnaissance**: Scansione porte e servizi
   - 🌐 **Web Intelligence**: Raccolta informazioni web
   - 🔒 **Vulnerability Assessment**: Identificazione vulnerabilità
   - 💥 **Exploitation**: Test di exploitation

3. **Generazione Report**: Al termine crea:
   - 📄 Report PDF dettagliato
   - 📊 Registrazione nel database SQL

### Esempio di Utilizzo

```
============================================================
🛡️  HEKA PENETRATION TESTING SYSTEM
============================================================

Inserisci il target da testare:
• Indirizzo IP (es: *************)
• Dominio (es: example.com)
• Range CIDR (es: ***********/24)

Digita 'exit' per uscire

🎯 Target: example.com

📊 Sessione creata: Auto-Pentest-20250125-143022
🎯 Target: example.com (domain)
🕐 Inizio: 2025-01-25 14:30:22
============================================================

🔍 FASE 1: Reconnaissance
----------------------------------------
✅ Reconnaissance completato

🌐 FASE 2: Web Intelligence
----------------------------------------
✅ Web Intelligence completato

🔒 FASE 3: Vulnerability Assessment
----------------------------------------
✅ Vulnerability Assessment completato

💥 FASE 4: Exploitation
----------------------------------------
✅ Exploitation completato

📄 Generazione report PDF...

✅ Penetration test completato!
⏱️  Durata totale: 15.3 minuti
📄 Report salvato: reports/pentest_report_Auto-Pentest-20250125-143022.pdf
```

## 🔧 Componenti Tecnici

### 🤖 Agenti Implementati

1. **Hacker Agent**: Penetration testing principale
2. **Web Intelligence Agent**: Raccolta informazioni web
3. **Kali Executor Agent**: Esecuzione comandi shell
4. **Developer Agent**: Supporto tecnico
5. **Researcher Agent**: Ricerca e analisi

### 🛠️ Tools Disponibili

- **KaliExecutorTool**: Esecuzione sicura comandi Kali Linux
- **WebBrowserTool**: Navigazione e scraping web con Playwright
- **Nmap Tool**: Scansione rete e porte
- **Metasploit Tool**: Framework exploitation

### 📊 Database Schema

```sql
-- Targets
CREATE TABLE targets (
    id INTEGER PRIMARY KEY,
    target_name TEXT NOT NULL,
    target_type TEXT NOT NULL,
    description TEXT,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP
);

-- Sessioni Penetration Test
CREATE TABLE pentest_sessions (
    id INTEGER PRIMARY KEY,
    target_id INTEGER,
    session_name TEXT NOT NULL,
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    status TEXT DEFAULT 'running',
    objective TEXT,
    report_path TEXT,
    FOREIGN KEY (target_id) REFERENCES targets (id)
);

-- Task degli Agenti
CREATE TABLE agent_tasks (
    id INTEGER PRIMARY KEY,
    session_id INTEGER,
    agent_name TEXT NOT NULL,
    task_description TEXT,
    task_type TEXT,
    start_time TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    end_time TIMESTAMP,
    status TEXT DEFAULT 'running',
    result TEXT,
    FOREIGN KEY (session_id) REFERENCES pentest_sessions (id)
);

-- Vulnerabilità Identificate
CREATE TABLE vulnerabilities (
    id INTEGER PRIMARY KEY,
    session_id INTEGER,
    vulnerability_name TEXT NOT NULL,
    severity TEXT NOT NULL,
    description TEXT,
    affected_service TEXT,
    proof_of_concept TEXT,
    remediation TEXT,
    discovered_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    FOREIGN KEY (session_id) REFERENCES pentest_sessions (id)
);
```

## 🧪 Testing

### Test Automatici

```bash
python3 test_pentest_manager.py
```

### Test Manuale

```bash
# Test validazione target
python3 -c "
from pentest_manager import PentestManager
manager = PentestManager()
print(manager.validate_target('*************'))  # True
print(manager.validate_target('example.com'))    # True
print(manager.validate_target('invalid'))        # False
"
```

## 📋 Requisiti

### Dipendenze Python

```bash
pip install crewai playwright reportlab python-dotenv
```

### Dipendenze Sistema

- **Kali Linux** (per tools di penetration testing)
- **Python 3.8+**
- **SQLite3**

### Configurazione

1. **File .env**: Configura variabili d'ambiente
2. **config/pentest_config.yaml**: Configurazione agenti
3. **prompts/**: Template prompt per agenti

## 🔒 Sicurezza

### Validazione Comandi

- Whitelist comandi permessi
- Sanitizzazione input
- Timeout per prevenire hang
- Logging completo attività

### Ethical Hacking

- Solo su target autorizzati
- Documentazione completa
- Rispetto limiti legali
- Report dettagliati per remediation

## 📈 Roadmap

### Prossime Funzionalità

- [ ] **Web UI**: Interfaccia web per gestione
- [ ] **API REST**: Endpoint per integrazione
- [ ] **Scheduling**: Penetration test programmati
- [ ] **Multi-Target**: Test paralleli su più target
- [ ] **Custom Modules**: Plugin personalizzati
- [ ] **Cloud Integration**: Deploy su cloud
- [ ] **Real-time Monitoring**: Dashboard live

### Miglioramenti

- [ ] **Performance**: Ottimizzazione velocità
- [ ] **Scalability**: Supporto grandi reti
- [ ] **Reporting**: Template report personalizzabili
- [ ] **Integration**: Connessione SIEM/SOAR
- [ ] **AI Enhancement**: Modelli AI specializzati

## 🤝 Contributi

Per contribuire al progetto:

1. Fork del repository
2. Crea feature branch
3. Implementa modifiche
4. Test completi
5. Pull request

## 📄 Licenza

Progetto per uso educativo e testing autorizzato.

---

**⚠️ Disclaimer**: Utilizzare solo su sistemi autorizzati. L'uso non autorizzato è illegale.
