# Configurazione Sistema Multi-Agent Penetration Testing

# Configurazione Agenti
agents:
  reconnaissance:
    role: "Reconnaissance Specialist"
    goal: "Raccogliere informazioni dettagliate sui target attraverso OSINT e scanning"
    max_iterations: 3
    allow_delegation: true
    verbose: true
    model: "qwen3_brain"
    agent_type: "hacker"
    
  exploitation:
    role: "Exploitation Expert"
    goal: "Identificare e sfruttare vulnerabilità per ottenere accesso ai sistemi"
    max_iterations: 3
    allow_delegation: true
    verbose: true
    model: "qwen3_coder"
    agent_type: "hacker"
    
  linux_admin:
    role: "Linux Systems Expert"
    goal: "Analizzare sistemi Linux, configurazioni e implementare hardening"
    max_iterations: 3
    allow_delegation: true
    verbose: true
    model: "qwen3_brain"
    agent_type: "hacker"
    
  reporting:
    role: "Security Analyst & Reporter"
    goal: "Analizzare risultati e creare report dettagliati di penetration testing"
    max_iterations: 2
    allow_delegation: false
    verbose: true
    model: "qwen3_brain"
    agent_type: "researcher"

  web_intelligence:
    role: "Web Intelligence Gatherer"
    goal: "Navigare sul web per raccogliere informazioni utili e supportare altri agenti"
    max_iterations: 3
    allow_delegation: true
    verbose: true
    model: "qwen3_brain"
    agent_type: "researcher"

  kali_executor:
    role: "Kali Linux Executor"
    goal: "Eseguire comandi e tools di penetration testing su Kali Linux seguendo istruzioni degli altri agenti"
    max_iterations: 5
    allow_delegation: false
    verbose: true
    model: "qwen3_brain"
    agent_type: "executor"

# Configurazione Strumenti
tools:
  network_scan:
    enabled: true
    timeout: 300
    default_options: "-sS -sV -O"
    
  vulnerability_scan:
    enabled: true
    timeout: 600
    scan_types: ["basic", "standard", "comprehensive"]
    
  exploit:
    enabled: true
    timeout: 180
    safety_checks: true
    
  linux_command:
    enabled: true
    timeout: 60
    allowed_commands: ["ps", "netstat", "ss", "systemctl", "find", "grep"]

  web_browser:
    enabled: true
    timeout: 120
    max_pages_per_session: 10
    headless: true
    user_agent: "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"

  kali_executor:
    enabled: true
    timeout: 300
    max_history: 1000
    allowed_commands: ["nmap", "nikto", "sqlmap", "gobuster", "dirb", "ping", "curl", "wget", "dig", "nslookup"]
    safety_checks: true

# Configurazione Task
tasks:
  reconnaissance:
    timeout: 1800  # 30 minuti
    required_outputs:
      - "network_scan_results"
      - "vulnerability_assessment"
      - "attack_surface_analysis"
      
  exploitation:
    timeout: 2400  # 40 minuti
    required_outputs:
      - "exploitation_results"
      - "access_level"
      - "evidence_collection"
      
  linux_admin:
    timeout: 1200  # 20 minuti
    required_outputs:
      - "system_analysis"
      - "hardening_recommendations"
      - "implementation_scripts"
      
  reporting:
    timeout: 900   # 15 minuti
    required_outputs:
      - "executive_summary"
      - "technical_details"
      - "remediation_plan"

  web_intelligence:
    timeout: 1500  # 25 minuti
    required_outputs:
      - "web_research_results"
      - "intelligence_summary"
      - "actionable_information"

  kali_executor:
    timeout: 2400  # 40 minuti
    required_outputs:
      - "command_execution_results"
      - "tool_output_analysis"
      - "vulnerability_validation"

# Configurazione Crew
crew:
  process: "sequential"  # sequential, hierarchical
  verbose: true
  memory: true
  cache: true
  max_rpm: 10  # Requests per minute
  
# Configurazione Output
output:
  format: "json"
  include_ai_stats: true
  save_to_file: true
  log_level: "INFO"
  
  report_sections:
    - "executive_summary"
    - "methodology"
    - "findings"
    - "evidence"
    - "recommendations"
    - "appendix"

# Configurazione Sicurezza
security:
  safe_mode: true
  require_authorization: true
  log_all_activities: true
  data_retention_days: 90
  
  allowed_targets:
    - "192.168.1.0/24"
    - "10.0.0.0/8"
    - "172.16.0.0/12"
    - "testlab.local"
    
  forbidden_actions:
    - "data_destruction"
    - "service_disruption"
    - "unauthorized_access"

# Configurazione Prompt
prompts:
  base_path: "prompts"
  cache_enabled: true
  
  agent_contexts:
    hacker: "hacker"
    researcher: "researcher"
    developer: "developer"
    
  custom_prompts:
    reconnaissance: "hacker/reconnaissance.md"
    exploitation: "hacker/exploitation.md"
    linux_admin: "hacker/linux_admin.md"

# Configurazione AI Client
ai_client:
  models:
    qwen3_brain:
      url_env: "QWEN3_BRAIN_API_URL"
      timeout: 60
      max_tokens: 2000
      temperature: 0.7
      
    qwen3_coder:
      url_env: "QWEN3_CODER_API_URL"
      timeout: 60
      max_tokens: 2000
      temperature: 0.5
      
  retry_attempts: 3
  retry_delay: 5

# Configurazione Logging
logging:
  level: "INFO"
  format: "%(asctime)s - %(name)s - %(levelname)s - %(message)s"
  
  handlers:
    file:
      enabled: true
      filename: "logs/pentest_manager.log"
      max_size: "10MB"
      backup_count: 5
      
    console:
      enabled: true
      level: "INFO"
      
    syslog:
      enabled: false
      facility: "local0"

# Configurazione Performance
performance:
  max_concurrent_tasks: 4
  task_timeout: 3600  # 1 ora
  memory_limit: "2GB"
  cpu_limit: 80  # percentuale

# Configurazione Notifiche
notifications:
  enabled: false
  
  email:
    smtp_server: "localhost"
    smtp_port: 587
    username: ""
    password: ""
    recipients: []
    
  slack:
    webhook_url: ""
    channel: "#security"
    
  webhook:
    url: ""
    headers: {}

# Configurazione Integrazione
integrations:
  metasploit:
    enabled: false
    rpc_host: "localhost"
    rpc_port: 55553
    
  nmap:
    enabled: true
    binary_path: "/usr/bin/nmap"
    
  burp_suite:
    enabled: false
    api_url: "http://localhost:1337"
    api_key: ""
    
  jira:
    enabled: false
    url: ""
    username: ""
    api_token: ""
    project_key: ""

# Configurazione Template
templates:
  pentest_report:
    format: "markdown"
    sections:
      - "executive_summary"
      - "scope_and_methodology"
      - "findings_summary"
      - "detailed_findings"
      - "recommendations"
      - "appendices"
      
  linux_hardening:
    format: "bash"
    include_comments: true
    backup_configs: true
