# Heka Multi-Agent System

**Heka** è un sistema multi-agente avanzato che utilizza agenti autonomi per automatizzare task di sicurezza informatica, bug bounty e penetration testing. Sviluppato da **b0**, Heka offre un'architettura modulare e sicura per l'esecuzione di comandi nel terminale attraverso agenti specializzati.

## 🚀 Caratteristiche Principali

- **Sistema Multi-Agente**: Architettura modulare con agenti specializzati
- **Esecuzione Sicura**: Validazione avanzata dei comandi con sistema di sicurezza integrato
- **Comunicazione Asincrona**: Message bus per comunicazione tra agenti
- **Monitoraggio in Tempo Reale**: Sistema di logging e monitoraggio delle attività
- **Configurazione Flessibile**: Sistema di configurazione YAML/JSON
- **Esecuzione Concorrente**: Supporto per esecuzione parallela di comandi
- **Container Docker**: Ambiente isolato basato su Kali Linux

## 🏗️ Architettura

Il sistema è composto da:

- **Agent Manager**: Gestore centrale degli agenti
- **Terminal Agent**: Agente specializzato per comandi terminale
- **Message Bus**: Sistema di comunicazione tra agenti
- **Security Monitor**: Sistema di sicurezza e validazione
- **Config Manager**: Gestore delle configurazioni

## 📋 Prerequisiti

- Python 3.8+
- [Conda](https://docs.conda.io/en/latest/) (raccomandato)
- [Docker](https://www.docker.com/) (per esecuzione in container)

## 🛠️ Installazione

### Metodo 1: Ambiente Conda (Raccomandato)

```bash
# Crea ambiente conda
conda create -n heka python=3.12
conda activate heka

# Installa dipendenze
pip install -r requirements.txt

# Installa dipendenze aggiuntive per testing
pip install playwright requests beautifulsoup4 python-dotenv
playwright install
```

### Metodo 2: Ambiente Virtuale Python

```bash
# Crea ambiente virtuale
python -m venv venv
source venv/bin/activate  # Linux/Mac
# oppure
venv\Scripts\activate     # Windows

# Installa dipendenze
pip install -r requirements.txt
```

## 🚀 Esecuzione

### Modalità Interattiva

```bash
# Attiva l'ambiente
conda activate heka

# Avvia il sistema in modalità interattiva
python app.py
```

### Demo del Sistema

```bash
# Esegui il demo completo
python demo.py
```

### Esecuzione con Docker

```bash
# Costruisci l'immagine Docker
docker build -t heka-kali .

# Esegui il container
docker run -it heka-kali
```

## 💻 Utilizzo

### Modalità Interattiva

Una volta avviato il sistema, puoi utilizzare i seguenti comandi:

```
heka> cmd ls -la                    # Esegue un comando
heka> script echo "Hello World"     # Esegue uno script
heka> status                        # Mostra stato sistema
heka> agents                        # Lista agenti attivi
heka> security                      # Statistiche sicurezza
heka> quit                          # Esce dal programma
```

### API Programmatica

```python
from app import HekaSystem
import asyncio

async def main():
    heka = HekaSystem()
    await heka.start()

    # Esegui un comando
    result = await heka.execute_command("echo 'Hello from Heka'")
    print(result)

    # Esegui uno script
    script = "#!/bin/bash\necho 'Script execution'\npwd"
    result = await heka.execute_script(script, "bash")
    print(result)

    await heka.stop()

asyncio.run(main())
```

## 🔧 Configurazione

Il sistema utilizza file di configurazione YAML per personalizzare il comportamento degli agenti:

### Configurazione Base (`config/agents.yaml`)

```yaml
system:
  log_level: "INFO"
  max_agents: 10
  message_queue_size: 1000

agents:
  terminal:
    log_level: "INFO"
    max_memory_mb: 256
    terminal:
      allowed_commands: []  # Lista vuota = tutti i comandi sicuri
      blocked_commands:
        - "rm -rf /"
        - "dd if="
        - "mkfs"
        - "shutdown"
      max_execution_time: 300
      working_directory: "/tmp/heka"
      security_level: "medium"  # low, medium, high, critical

default_agents:
  - type: "terminal"
    config:
      working_directory: "/tmp/heka"
      security_level: "medium"
```

### Livelli di Sicurezza

- **LOW**: Controlli minimi, massima flessibilità
- **MEDIUM**: Bilanciamento tra sicurezza e funzionalità (default)
- **HIGH**: Controlli rigorosi, comandi privilegiati bloccati
- **CRITICAL**: Modalità whitelist, solo comandi esplicitamente permessi

## 🛡️ Sicurezza

Il sistema implementa multiple layer di sicurezza:

### Validazione Comandi
- Pattern matching per comandi pericolosi
- Blacklist di comandi privilegiati
- Controllo accesso a percorsi sensibili
- Validazione lunghezza comandi

### Monitoraggio
- Logging completo di tutte le attività
- Tracking eventi di sicurezza
- Alert automatici per minacce critiche
- Statistiche in tempo reale

### Isolamento
- Esecuzione in directory controllate
- Timeout per prevenire comandi infiniti
- Gestione processi con terminazione forzata
- Ambiente containerizzato opzionale

## 🧪 Testing

```bash
# Esegui tutti i test
python -m pytest tests/ -v

# Test specifici
python -m pytest tests/test_multi_agent_system.py::TestTerminalAgent -v

# Test con coverage
pip install pytest-cov
python -m pytest tests/ --cov=agents --cov-report=html
```

## 📊 Monitoraggio

### Statistiche Sistema

```python
# Ottieni statistiche complete
status = heka.get_system_status()
print(status)

# Output esempio:
{
  "status": "running",
  "agent_stats": {
    "total_agents": 2,
    "tasks_completed": 15,
    "success_rate": 0.93
  },
  "security_stats": {
    "blocked_commands": 3,
    "security_violations": 1,
    "threat_level_counts": {...}
  }
}
```

### Log Files

- `logs/heka.log`: Log principale del sistema
- `logs/security.log`: Log eventi di sicurezza
- `config/agents.yaml`: Configurazione agenti

## 🔌 Estensibilità

### Creare Nuovi Agenti

```python
from agents.base_agent import BaseAgent, AgentType

class CustomAgent(BaseAgent):
    def __init__(self, agent_id=None, config=None):
        super().__init__(agent_id, AgentType.CUSTOM, config)

    def _define_capabilities(self):
        return ["custom_task", "special_operation"]

    async def process_message(self, message):
        # Implementa logica personalizzata
        pass

    async def execute_task(self, task):
        # Implementa esecuzione task
        pass
```

### Aggiungere Handler Personalizzati

```python
async def custom_message_handler(message):
    print(f"Messaggio personalizzato: {message.content}")

# Registra handler
heka.agent_manager.message_bus.register_message_handler(
    "custom_type",
    custom_message_handler
)
```

## 🐛 Troubleshooting

### Problemi Comuni

**Comando bloccato inaspettatamente**
```bash
# Controlla i log di sicurezza
tail -f logs/security.log

# Modifica configurazione sicurezza
# Edita config/agents.yaml e riavvia
```

**Agente non risponde**
```bash
# Verifica stato agenti
heka> agents

# Riavvia sistema se necessario
heka> quit
python app.py
```

**Errori di permessi**
```bash
# Verifica directory di lavoro
ls -la /tmp/heka

# Crea directory se necessario
mkdir -p /tmp/heka
chmod 755 /tmp/heka
```

## 🤝 Contribuire

1. Fork del repository
2. Crea branch feature (`git checkout -b feature/amazing-feature`)
3. Commit delle modifiche (`git commit -m 'Add amazing feature'`)
4. Push al branch (`git push origin feature/amazing-feature`)
5. Apri una Pull Request

### Linee Guida

- Segui PEP 8 per lo stile del codice
- Aggiungi test per nuove funzionalità
- Aggiorna la documentazione
- Usa commit message descrittivi

## 📄 Licenza

Questo progetto è rilasciato sotto licenza MIT. Vedi il file `LICENSE` per i dettagli.

## 👨‍💻 Autore

**b0** - Sviluppatore principale

## 🙏 Ringraziamenti

- Comunità open source per le librerie utilizzate
- Contributori e tester del progetto
- Team di sicurezza per feedback e suggerimenti

## 📚 Documentazione Aggiuntiva

- [API Reference](docs/api.md)
- [Security Guide](docs/security.md)
- [Development Guide](docs/development.md)
- [Examples](examples/)

---

**⚠️ Disclaimer**: Questo strumento è destinato esclusivamente a scopi educativi e di testing autorizzato. L'uso improprio è responsabilità dell'utente.


