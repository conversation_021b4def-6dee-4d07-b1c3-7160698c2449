#!/usr/bin/env python3
"""
Heka Collaborative Penetration Testing Manager
Sistema ristrutturato con agenti AI collaborativi continui

Caratteristiche:
- Agenti AI che si scambiano informazioni continuamente
- Ciclo continuo fino al completamento dell'obiettivo
- Web Intelligence con dorks personalizzate e test di link
- Agente terminale che esegue comandi e condivide output
- Analisi collaborativa dei risultati per trovare soluzioni

Utilizzo:
    python3 collaborative_pentest_manager.py
    
Autore: Heka Team
Versione: 3.0 - Collaborative AI
"""

import asyncio
import logging
import os
import sys
import re
import json
from datetime import datetime
from typing import Dict, Any, Optional

# Aggiungi il path del progetto per gli import
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

# Carica variabili d'ambiente dal file .env
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # Se python-dotenv non è installato, prova a caricare manualmente
    if os.path.exists('.env'):
        with open('.env', 'r') as f:
            for line in f:
                if line.strip() and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value

from agents.collaborative_crew import CollaborativeCrew
from agents.ai_client import ai_client
from database.pentest_database import pentest_db
from reports.pdf_generator import pdf_generator


class CollaborativePentestManager:
    """
    Manager per penetration testing collaborativo con agenti AI continui
    """

    def __init__(self):
        self.setup_logging()
        self.logger = logging.getLogger("heka.collaborative_manager")
        self.crew = CollaborativeCrew()
        self.current_session_id = None
        self.current_target_id = None

    def setup_logging(self):
        """Configura il logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/collaborative_pentest.log'),
                logging.StreamHandler()
            ]
        )

        # Crea directory logs se non esiste
        os.makedirs('logs', exist_ok=True)
        os.makedirs('reports', exist_ok=True)

    def get_target_input(self) -> str:
        """Ottiene input target dall'utente con validazione"""
        print("\n🛡️  HEKA COLLABORATIVE PENETRATION TESTING SYSTEM")
        print("=" * 60)
        print("Sistema di Penetration Testing con Agenti AI Collaborativi")
        print("=" * 60)
        
        while True:
            print("\n🎯 Inserisci il target da testare:")
            print("   • Indirizzo IP (es: *************)")
            print("   • Dominio (es: example.com)")
            print("   • Range CIDR (es: ***********/24)")
            print("   • URL (es: https://example.com)")
            
            target = input("\n🎯 Target: ").strip()
            
            if not target:
                print("❌ Target non può essere vuoto!")
                continue
            
            # Validazione base
            if self._validate_target(target):
                return target
            else:
                print("❌ Target non valido! Riprova.")

    def _validate_target(self, target: str) -> bool:
        """Valida il target inserito"""
        # Pattern per IP
        ip_pattern = r'^(\d{1,3}\.){3}\d{1,3}$'
        # Pattern per dominio
        domain_pattern = r'^[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9\-]{0,61}[a-zA-Z0-9])?)*$'
        # Pattern per CIDR
        cidr_pattern = r'^(\d{1,3}\.){3}\d{1,3}/\d{1,2}$'
        # Pattern per URL
        url_pattern = r'^https?://[^\s/$.?#].[^\s]*$'
        
        if (re.match(ip_pattern, target) or 
            re.match(domain_pattern, target) or 
            re.match(cidr_pattern, target) or
            re.match(url_pattern, target)):
            return True
        
        return False

    def get_objective_input(self) -> str:
        """Ottiene l'obiettivo del penetration test"""
        print("\n🎯 Definisci l'obiettivo del penetration test:")
        print("   Esempi:")
        print("   • Trova vulnerabilità web nel target")
        print("   • Ottieni accesso al sistema target")
        print("   • Identifica servizi esposti e vulnerabilità")
        print("   • Testa la sicurezza dell'applicazione web")
        
        while True:
            objective = input("\n🎯 Obiettivo: ").strip()
            if objective:
                return objective
            print("❌ L'obiettivo non può essere vuoto!")

    async def run_collaborative_pentest(self, target: str, objective: str) -> Dict[str, Any]:
        """
        Esegue penetration test collaborativo continuo
        """
        start_time = datetime.now()
        
        try:
            self.logger.info(f"Avvio penetration test collaborativo su {target}")
            
            # 1. Determina tipo di target
            target_type = self._determine_target_type(target)
            
            # 2. Crea target e sessione nel database
            target_id = pentest_db.create_target(target, target_type, f"Collaborative pentest - {objective}")
            session_name = f"Collaborative_Pentest_{target}_{start_time.strftime('%Y%m%d_%H%M%S')}"
            session_id = pentest_db.create_pentest_session(target_id, session_name, objective)
            
            self.current_target_id = target_id
            self.current_session_id = session_id
            self.crew.session_id = session_id

            print(f"\n📊 Sessione collaborativa creata: {session_name}")
            print(f"🎯 Target: {target} ({target_type})")
            print(f"🎯 Obiettivo: {objective}")
            print(f"🕐 Inizio: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print("\n" + "="*60)
            print("🤖 Avvio agenti collaborativi...")
            print("="*60)

            # 3. Esegui penetration test collaborativo
            results = await self.crew.execute_collaborative_pentest(target, objective)

            # 4. Genera report PDF
            print("\n📄 Generazione report PDF...")
            session_data = pentest_db.get_session_summary(session_id)
            report_path = f"reports/collaborative_pentest_report_{session_name}.pdf"
            pdf_generator.generate_pentest_report(session_data, report_path)

            # 5. Completa sessione
            pentest_db.complete_pentest_session(session_id, results['status'], report_path)

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            # 6. Compila risultati finali
            final_results = {
                "target": target,
                "target_type": target_type,
                "objective": objective,
                "session_id": session_id,
                "session_name": session_name,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "duration_seconds": duration,
                "status": results['status'],
                "progress": results['progress'],
                "iterations": results['iterations'],
                "findings": results['findings'],
                "agents_involved": results['agents_involved'],
                "total_messages": results['total_messages'],
                "report_path": report_path
            }

            self.logger.info(f"Penetration test completato: {results['status']}")
            return final_results

        except Exception as e:
            self.logger.error(f"Errore durante penetration test: {e}")
            if self.current_session_id:
                pentest_db.complete_pentest_session(self.current_session_id, "failed")
            
            return {
                "status": "failed",
                "error": str(e),
                "target": target,
                "objective": objective
            }

    def _determine_target_type(self, target: str) -> str:
        """Determina il tipo di target"""
        if target.startswith(('http://', 'https://')):
            return "url"
        elif '/' in target and target.split('/')[-1].isdigit():
            return "cidr"
        elif re.match(r'^(\d{1,3}\.){3}\d{1,3}$', target):
            return "ip"
        else:
            return "domain"

    def display_results(self, results: Dict[str, Any]):
        """Visualizza i risultati finali"""
        print("\n" + "="*60)
        print("📋 RISULTATI PENETRATION TEST COLLABORATIVO")
        print("="*60)
        
        print(f"🎯 Target: {results.get('target', 'N/A')}")
        print(f"🎯 Obiettivo: {results.get('objective', 'N/A')}")
        print(f"📊 Stato: {results.get('status', 'N/A')}")
        print(f"📈 Progresso: {results.get('progress', 0):.1%}")
        print(f"🔄 Iterazioni: {results.get('iterations', 0)}")
        print(f"🤖 Agenti coinvolti: {', '.join(results.get('agents_involved', []))}")
        print(f"💬 Messaggi scambiati: {results.get('total_messages', 0)}")
        
        if results.get('duration_seconds'):
            duration_min = results['duration_seconds'] / 60
            if duration_min < 1:
                print(f"⏱️  Durata: {results['duration_seconds']:.1f} secondi")
            else:
                print(f"⏱️  Durata: {duration_min:.1f} minuti")
        
        if results.get('report_path'):
            print(f"📄 Report: {results['report_path']}")
        
        # Mostra findings principali
        findings = results.get('findings', [])
        if findings:
            print(f"\n🔍 Findings principali ({len(findings)}):")
            for i, finding in enumerate(findings[-5:], 1):  # Ultimi 5
                finding_type = finding.get('type', 'unknown')
                content = str(finding.get('content', ''))[:100]
                print(f"   {i}. {finding_type}: {content}...")
        
        if results.get('status') == 'failed':
            print(f"\n❌ Errore: {results.get('error', 'Errore sconosciuto')}")
        elif results.get('status') == 'completed':
            print(f"\n✅ Penetration test completato con successo!")
        else:
            print(f"\n⚠️  Penetration test interrotto")


async def main():
    """Funzione principale"""
    manager = CollaborativePentestManager()

    try:
        # Inizializza il sistema
        await ai_client.start()

        # Ottieni target e obiettivo dall'utente
        target = manager.get_target_input()
        objective = manager.get_objective_input()

        # Esegui penetration test collaborativo
        results = await manager.run_collaborative_pentest(target, objective)

        # Mostra risultati finali
        manager.display_results(results)

    except KeyboardInterrupt:
        print("\n\n⚠️  Penetration test interrotto dall'utente")
    except Exception as e:
        print(f"\n❌ Errore critico: {e}")
        logging.error(f"Errore critico in main: {e}")
    finally:
        # Cleanup
        try:
            await ai_client.stop()
        except:
            pass


if __name__ == "__main__":
    asyncio.run(main())
