# 🛡️ Heka Collaborative AI Penetration Testing System

Heka è stato completamente ristrutturato per supportare un sistema di **agenti AI collaborativi** che lavorano in modalità continua per raggiungere obiettivi di penetration testing specifici.

## 🔄 Nuova Architettura Collaborativa

### Caratteristiche Rivoluzionarie

- **🤖 Agenti AI Collaborativi**: Gli agenti si scambiano informazioni continuamente
- **🔄 Ciclo Continuo**: Esecuzione continua fino al completamento dell'obiettivo
- **💬 Comunicazione Inter-Agent**: Sistema di messaggistica avanzato tra agenti
- **🧠 Analisi Collaborativa**: Analisi condivisa dei risultati per trovare soluzioni
- **🎯 Obiettivi Dinamici**: Adattamento dinamico degli obiettivi basato sui risultati
- **📊 Tracking Completo**: Database SQL per tutte le comunicazioni e attività

## 🤖 Agenti Specializzati

### 1. Intelligence Gatherer
- **Ruolo**: Raccolta informazioni su metodologie di testing
- **Specializzazione**: Ricerca di informazioni su come eseguire test specifici
- **Strumenti**: Web browsing, knowledge base
- **Output**: Raccomandazioni per test e metodologie

### 2. Web Search Specialist  
- **Ruolo**: Ricerche web con dorks personalizzate e test di link
- **Specializzazione**: OSINT, Google Dorks, analisi contenuti web
- **Strumenti**: Playwright, motori di ricerca, web scraping
- **Output**: Vulnerabilità note, exploit disponibili, informazioni target

### 3. Terminal Executor
- **Ruolo**: Esecuzione comandi forniti dagli altri agenti
- **Specializzazione**: Kali Linux tools, penetration testing
- **Strumenti**: Nmap, Nikto, SQLMap, Gobuster, Hydra, etc.
- **Output**: Risultati comandi, output dettagliati per analisi

### 4. Analysis Coordinator
- **Ruolo**: Analisi output e coordinamento prossime azioni
- **Specializzazione**: Analisi risultati, pianificazione strategica
- **Strumenti**: AI analysis, pattern recognition
- **Output**: Piano azioni, valutazione progresso, decisioni strategiche

## 🔄 Flusso Collaborativo

```mermaid
graph TD
    A[Intelligence Gatherer] -->|Richieste ricerca| B[Web Search Specialist]
    B -->|Vulnerabilità trovate| C[Terminal Executor]
    C -->|Output comandi| D[Analysis Coordinator]
    D -->|Piano azioni| A
    D -->|Comandi specifici| C
    D -->|Nuove ricerche| B
    A -->|Condivisione conoscenza| B
    B -->|Condivisione conoscenza| A
    C -->|Condivisione conoscenza| D
    D -->|Condivisione conoscenza| A
```

## 🚀 Modalità di Utilizzo

### Modalità 1: Collaborativa (Raccomandato)
```bash
# Avvio sistema collaborativo
python3 pentest_manager.py
# Seleziona opzione 1 - Modalità Collaborativa

# Oppure direttamente
python3 collaborative_pentest_manager.py

# Oppure con script
./start_collaborative_heka.sh
```

### Modalità 2: Classica
```bash
# Avvio sistema classico
python3 pentest_manager.py
# Seleziona opzione 2 - Modalità Classica
```

## 📋 Esempio di Sessione Collaborativa

```
🛡️  HEKA COLLABORATIVE PENETRATION TESTING SYSTEM
============================================================

🔧 Seleziona modalità: 1 (Collaborativa)

🎯 Target: example.com
🎯 Obiettivo: Trova vulnerabilità web nel target

📊 Sessione collaborativa creata: Collaborative_Pentest_example.com_20241225_143022
🤖 Avvio agenti collaborativi...

Iterazione 1/50:
- Intelligence Gatherer: Raccolta informazioni su web testing
- Web Search Specialist: Ricerca vulnerabilità note per example.com
- Terminal Executor: nmap -sC -sV example.com
- Analysis Coordinator: Analisi risultati, progresso 25%

Iterazione 2/50:
- Web Search Specialist: Ricerca exploit per Apache 2.4.41
- Terminal Executor: nikto -h example.com
- Analysis Coordinator: Vulnerabilità identificata, progresso 60%

...

✅ Penetration test completato con successo!
📄 Report: reports/collaborative_pentest_report_Collaborative_Pentest_example.com_20241225_143022.pdf
```

## 🔧 Configurazione

### File .env
```bash
# API Keys
OPENAI_API_KEY=your_openai_api_key_here
ANTHROPIC_API_KEY=your_anthropic_api_key_here

# Collaborative System
MAX_ITERATIONS=50
AGENT_TIMEOUT=300
MESSAGE_RETENTION_HOURS=24

# Web Intelligence
WEB_SEARCH_TIMEOUT=30
MAX_SEARCH_RESULTS=10
PLAYWRIGHT_HEADLESS=true

# Kali Tools
KALI_TOOLS_TIMEOUT=120
```

## 📊 Database Schema

### Nuove Tabelle per Sistema Collaborativo

```sql
-- Comunicazioni tra agenti
CREATE TABLE agent_communications (
    id INTEGER PRIMARY KEY,
    session_id INTEGER,
    from_agent TEXT,
    to_agent TEXT,
    message_type TEXT,
    message_content TEXT,
    timestamp DATETIME,
    metadata TEXT
);

-- Stato obiettivi
CREATE TABLE objective_status (
    id INTEGER PRIMARY KEY,
    session_id INTEGER,
    target TEXT,
    objective TEXT,
    status TEXT,
    progress REAL,
    completion_criteria TEXT,
    timestamp DATETIME
);
```

## 🎯 Criteri di Completamento

Il sistema collaborativo continua fino a quando:

1. **Vulnerabilità Identificata**: Almeno una vulnerabilità sfruttabile trovata
2. **Accesso Ottenuto**: Accesso al sistema target raggiunto
3. **Progresso Sufficiente**: Almeno 80% di progresso verso l'obiettivo
4. **Iterazioni Massime**: Raggiunto il limite di 50 iterazioni
5. **Obiettivo Specifico**: Completamento dell'obiettivo definito dall'utente

## 🔍 Vantaggi del Sistema Collaborativo

### Rispetto al Sistema Classico:

- **🔄 Continuità**: Non si ferma fino al raggiungimento dell'obiettivo
- **🧠 Intelligenza Collettiva**: Agenti che imparano l'uno dall'altro
- **⚡ Adattabilità**: Cambia strategia basandosi sui risultati
- **🎯 Precisione**: Focus specifico sull'obiettivo definito
- **📈 Efficienza**: Evita test ridondanti attraverso comunicazione
- **🔍 Profondità**: Analisi più approfondita dei risultati

## 📝 Log e Monitoraggio

```bash
# Log sistema collaborativo
tail -f logs/collaborative_pentest.log

# Monitoraggio database
sqlite3 heka_pentest.db "SELECT * FROM agent_communications ORDER BY timestamp DESC LIMIT 10;"
```

## 🚀 Prossimi Sviluppi

- **Machine Learning**: Apprendimento da sessioni precedenti
- **Plugin System**: Agenti personalizzabili
- **Distributed Execution**: Esecuzione distribuita su più sistemi
- **Real-time Dashboard**: Dashboard web per monitoraggio live
- **API Integration**: Integrazione con altri security tools

---

**Heka Collaborative System** - Penetration Testing del futuro con AI collaborativa! 🛡️🤖
