# Web Intelligence Agent

## Panoramica

Il **Web Intelligence Agent** è un nuovo agente specializzato nella raccolta di informazioni strategiche dal web per supportare le operazioni di penetration testing. Utilizza Playwright per navigazione web avanzata e fornisce intelligence mirata agli altri agenti del sistema Heka.

## Caratteristiche Principali

### 🔍 Capacità di Ricerca
- **OSINT (Open Source Intelligence)**: Raccolta informazioni pubbliche
- **Ricerca su motori di ricerca**: DuckDuckGo, Google
- **Navigazione web intelligente**: JavaScript rendering, estrazione contenuti
- **Analisi mirata**: Ricerca di informazioni specifiche richieste da altri agenti

### 🌐 Tecnologie Utilizzate
- **Playwright**: Browser automation per navigazione avanzata
- **Headless browsing**: Navigazione non visibile e efficiente
- **JavaScript rendering**: Supporto per contenuti dinamici
- **Multi-engine search**: Supporto per diversi motori di ricerca

### 🤝 Integrazione con Altri Agenti
- **Reconnaissance Support**: Fornisce informazioni OSINT per il reconnaissance
- **Exploitation Intelligence**: Ricerca exploit e vulnerabilità note
- **Linux Administration**: Trova guide e best practices
- **Reporting Data**: Raccoglie informazioni per report completi

## Configurazione

### Dipendenze
```bash
# Installa Playwright
pip install playwright

# Installa browser
playwright install
```

### Configurazione YAML
```yaml
agents:
  web_intelligence:
    role: "Web Intelligence Gatherer"
    goal: "Navigare sul web per raccogliere informazioni utili e supportare altri agenti"
    max_iterations: 3
    allow_delegation: true
    verbose: true
    model: "qwen3_brain"
    agent_type: "researcher"

tools:
  web_browser:
    enabled: true
    timeout: 120
    max_pages_per_session: 10
    headless: true
    user_agent: "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36"
```

## Utilizzo

### Comando CLI
```bash
# Ricerca web intelligence
./pentest_manager.py --mode web_intel --query "OWASP Top 10 vulnerabilities 2023"

# Ricerca per supportare reconnaissance
./pentest_manager.py --mode web_intel --query "example.com security vulnerabilities CVE"
```

### Utilizzo Programmatico
```python
from agents.pentest_crew import pentest_crew

# Esegui ricerca web intelligence
result = pentest_crew.execute_web_intelligence("Apache 2.4.41 exploits")
print(result)
```

### Demo Interattiva
```bash
# Avvia demo
python3 demo/demo_pentest.py

# Scegli opzione 5: Demo Web Intelligence Agent
```

## Funzionalità del Web Browser Tool

### Ricerca Web
```python
# Ricerca su motori di ricerca
tool._run(
    query="penetration testing tools",
    action="search",
    max_results=5,
    search_engine="duckduckgo"
)
```

### Navigazione
```python
# Naviga verso un URL specifico
tool._run(
    query="https://example.com",
    action="navigate",
    extract_links=True,
    extract_text=True
)
```

### Analisi Mirata
```python
# Analizza una pagina cercando informazioni specifiche
tool._run(
    query="https://example.com",
    action="analyze",
    look_for="security vulnerabilities",
    deep_analysis=True
)
```

## Esempi di Utilizzo

### Supporto al Reconnaissance
```
Query: "example.com services exposed ports"
Risultato: Lista di servizi noti, porte comuni, configurazioni pubbliche
```

### Supporto all'Exploitation
```
Query: "Apache 2.4.41 CVE exploits"
Risultato: CVE correlate, exploit pubblici, proof of concept
```

### Supporto Linux Administration
```
Query: "Ubuntu 20.04 security hardening best practices"
Risultato: Guide ufficiali, checklist CIS, raccomandazioni NIST
```

## Sicurezza e Best Practices

### Navigazione Responsabile
- **Rispetto robots.txt**: Segue le direttive dei siti web
- **Rate limiting**: Evita sovraccarico dei server
- **User agent appropriato**: Identificazione trasparente
- **Gestione sessioni**: Limite di pagine per sessione

### Privacy e Conformità
- **Navigazione headless**: Modalità non visibile
- **Gestione tracciamento**: Minimizzazione delle tracce
- **Dati sensibili**: Evita raccolta di informazioni personali
- **Conformità legale**: Rispetto delle normative applicabili

## Output Format

### Struttura Standard
```json
{
  "request_id": "unique_identifier",
  "search_query": "original_request",
  "sources_consulted": ["url1", "url2", "url3"],
  "information_found": {
    "summary": "brief_overview",
    "details": "comprehensive_information",
    "actionable_items": ["action1", "action2"],
    "confidence_level": "high/medium/low"
  },
  "recommendations": "next_steps_suggestions",
  "timestamp": "search_completion_time"
}
```

## Test e Validazione

### Test Automatici
```bash
# Esegui test completi
python3 test/test_web_intelligence.py

# Test specifici
python3 -c "
from test.test_web_intelligence import test_web_browser_tool
test_web_browser_tool()
"
```

### Test Manuali
```bash
# Test ricerca semplice
./pentest_manager.py --mode web_intel --query "OWASP Top 10"

# Test ricerca specifica
./pentest_manager.py --mode web_intel --query "CVE-2023-1234 exploit"
```

## Troubleshooting

### Problemi Comuni

#### Playwright non installato
```bash
pip install playwright
playwright install
```

#### Timeout durante navigazione
- Aumentare il timeout nella configurazione
- Verificare la connessione internet
- Controllare se il sito è accessibile

#### Errori di estrazione contenuti
- Verificare che il sito supporti JavaScript
- Controllare i selettori CSS utilizzati
- Testare con siti più semplici

### Log e Debug
```bash
# Abilita logging dettagliato
export HEKA_LOG_LEVEL=DEBUG

# Esegui con verbose
./pentest_manager.py --mode web_intel --query "test" --verbose
```

## Roadmap e Sviluppi Futuri

### Funzionalità Pianificate
- **Supporto proxy**: Navigazione attraverso proxy/VPN
- **Cache intelligente**: Memorizzazione risultati ricerche
- **Analisi sentiment**: Valutazione del sentiment delle informazioni
- **Integrazione API**: Connessione diretta a database CVE/NVD

### Miglioramenti
- **Performance**: Ottimizzazione velocità di ricerca
- **Accuratezza**: Miglioramento algoritmi di estrazione
- **Scalabilità**: Supporto per ricerche parallele
- **Personalizzazione**: Configurazioni specifiche per dominio

## Contribuire

Per contribuire allo sviluppo del Web Intelligence Agent:

1. **Fork** del repository
2. **Crea** un branch per la feature
3. **Implementa** le modifiche
4. **Testa** con la test suite
5. **Crea** una pull request

### Linee Guida
- Seguire le convenzioni di codice esistenti
- Aggiungere test per nuove funzionalità
- Documentare le modifiche
- Rispettare i principi di sicurezza e privacy
