"""
AI Client per gli agenti Heka
Client per comunicare con i modelli AI tramite API
"""

import asyncio
import aiohttp
import json
import os
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from enum import Enum
from datetime import datetime

# Carica variabili d'ambiente dal file .env se non già caricate
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # Se python-dotenv non è installato, prova a caricare manualmente
    if os.path.exists('.env'):
        with open('.env', 'r') as f:
            for line in f:
                if line.strip() and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value

class AIModel(Enum):
    QWEN3_CODER = "qwen3_coder"
    QWEN3_BRAIN = "qwen3_brain"

@dataclass
class AIRequest:
    """Richiesta AI"""
    prompt: str
    model: AIModel
    context: str = ""
    max_tokens: int = 2000
    temperature: float = 0.7
    agent_type: str = "terminal"
    task_type: str = "general"

@dataclass
class AIResponse:
    """Risposta AI"""
    content: str
    model: AIModel
    success: bool
    error: Optional[str] = None
    tokens_used: int = 0
    response_time: float = 0.0
    timestamp: datetime = None
    
    def __post_init__(self):
        if self.timestamp is None:
            self.timestamp = datetime.now()

class AIClient:
    """Client per comunicare con i modelli AI"""
    
    def __init__(self):
        self.logger = logging.getLogger("heka.ai_client")
        
        # Carica configurazione da .env
        self.api_urls = {
            AIModel.QWEN3_CODER: os.getenv("QWEN3_CODER_API_URL"),
            AIModel.QWEN3_BRAIN: os.getenv("QWEN3_BRAIN_API_URL")
        }
        
        # Verifica configurazione
        for model, url in self.api_urls.items():
            if not url:
                self.logger.warning(f"URL API non configurato per {model.value}")
        
        # Statistiche
        self.requests_count = 0
        self.successful_requests = 0
        self.failed_requests = 0
        self.total_tokens = 0
        
        # Session HTTP riutilizzabile
        self.session: Optional[aiohttp.ClientSession] = None
    
    async def start(self):
        """Avvia il client AI"""
        if not self.session:
            try:
                timeout = aiohttp.ClientTimeout(total=60)
                self.session = aiohttp.ClientSession(timeout=timeout)
                self.logger.info("AI Client avviato")
            except Exception as e:
                self.logger.error(f"Errore avvio client AI: {e}")
                # Fallback senza timeout
                self.session = aiohttp.ClientSession()
                self.logger.info("AI Client avviato senza timeout")
    
    async def stop(self):
        """Ferma il client AI"""
        if self.session:
            await self.session.close()
            self.session = None
            self.logger.info("AI Client fermato")
    
    async def send_request(self, request: AIRequest) -> AIResponse:
        """Invia richiesta AI"""
        if not self.session:
            await self.start()
        
        start_time = asyncio.get_event_loop().time()
        self.requests_count += 1
        
        try:
            # Ottieni URL per il modello
            api_url = self.api_urls.get(request.model)
            if not api_url:
                raise ValueError(f"URL API non configurato per {request.model.value}")
            
            # Prepara payload nel formato corretto
            # Combina context e prompt come richiesto
            full_text = f"{request.context}, {request.prompt}" if request.context else request.prompt

            payload = {
                "text": full_text
            }
            
            self.logger.debug(f"Invio richiesta POST a {request.model.value}")
            self.logger.debug(f"Payload: {payload}")

            # Invia richiesta POST con headers corretti
            async with self.session.post(
                api_url,
                json=payload,
                headers={'Content-Type': 'application/json'}
            ) as response:
                response_time = asyncio.get_event_loop().time() - start_time
                
                if response.status == 200:
                    data = await response.json()
                    self.logger.debug(f"Risposta API ricevuta: {data}")

                    # Estrai contenuto dalla risposta nel formato specificato
                    # Formato atteso: {"data": str, "quest": str, "response": str}
                    content = data.get("response", "")

                    # Se response è vuoto, prova con data come fallback
                    if not content:
                        content = data.get("data", "")

                    # Calcola token approssimativi se non forniti
                    tokens_used = data.get("tokens_used", len(content.split()) if content else 0)
                    
                    self.successful_requests += 1
                    self.total_tokens += tokens_used

                    self.logger.debug(f"Risposta ricevuta da {request.model.value} in {response_time:.2f}s")
                    self.logger.debug(f"Contenuto estratto: {content[:200]}..." if len(content) > 200 else f"Contenuto: {content}")
                    
                    return AIResponse(
                        content=content,
                        model=request.model,
                        success=True,
                        tokens_used=tokens_used,
                        response_time=response_time
                    )
                else:
                    error_text = await response.text()
                    raise Exception(f"HTTP {response.status}: {error_text}")
                    
        except Exception as e:
            self.failed_requests += 1
            response_time = asyncio.get_event_loop().time() - start_time
            
            error_msg = f"Errore richiesta AI: {str(e)}"
            self.logger.error(error_msg)
            
            return AIResponse(
                content="",
                model=request.model,
                success=False,
                error=error_msg,
                response_time=response_time
            )
    
    async def chat_completion(self, prompt: str, model: AIModel = AIModel.QWEN3_BRAIN,
                            context: str = "", agent_type: str = "terminal",
                            task_type: str = "general") -> str:
        """Completamento chat semplificato"""
        request = AIRequest(
            prompt=prompt,
            model=model,
            context=context,
            agent_type=agent_type,
            task_type=task_type
        )
        
        response = await self.send_request(request)
        
        if response.success:
            return response.content
        else:
            raise Exception(f"Errore AI: {response.error}")
    
    async def code_completion(self, prompt: str, context: str = "",
                            agent_type: str = "terminal") -> str:
        """Completamento codice usando QWEN3_CODER"""
        return await self.chat_completion(
            prompt=prompt,
            model=AIModel.QWEN3_CODER,
            context=context,
            agent_type=agent_type,
            task_type="code_generation"
        )
    
    async def analysis_completion(self, prompt: str, context: str = "",
                                agent_type: str = "hacker") -> str:
        """Analisi usando QWEN3_BRAIN"""
        return await self.chat_completion(
            prompt=prompt,
            model=AIModel.QWEN3_BRAIN,
            context=context,
            agent_type=agent_type,
            task_type="analysis"
        )
    
    def get_stats(self) -> Dict[str, Any]:
        """Restituisce statistiche del client AI"""
        success_rate = self.successful_requests / max(self.requests_count, 1)
        
        return {
            "total_requests": self.requests_count,
            "successful_requests": self.successful_requests,
            "failed_requests": self.failed_requests,
            "success_rate": success_rate,
            "total_tokens_used": self.total_tokens,
            "average_tokens_per_request": self.total_tokens / max(self.successful_requests, 1),
            "configured_models": [model.value for model, url in self.api_urls.items() if url]
        }

class PromptManager:
    """Gestore dei prompt e context"""
    
    def __init__(self, prompts_dir: str = "prompts"):
        self.prompts_dir = prompts_dir
        self.logger = logging.getLogger("heka.prompt_manager")
        self._cache: Dict[str, str] = {}
    
    def load_prompt(self, agent_type: str, prompt_name: str) -> str:
        """Carica un prompt specifico per un agente"""
        cache_key = f"{agent_type}:{prompt_name}"
        
        if cache_key in self._cache:
            return self._cache[cache_key]
        
        # Percorsi da provare
        paths_to_try = [
            f"{self.prompts_dir}/{agent_type}/{prompt_name}",
            f"{self.prompts_dir}/default/{prompt_name}",
            f"{self.prompts_dir}/heka/{prompt_name}"
        ]
        
        for path in paths_to_try:
            if os.path.exists(path):
                try:
                    with open(path, 'r', encoding='utf-8') as f:
                        content = f.read()
                        self._cache[cache_key] = content
                        return content
                except Exception as e:
                    self.logger.warning(f"Errore caricamento prompt {path}: {e}")
        
        self.logger.warning(f"Prompt non trovato: {agent_type}:{prompt_name}")
        return ""
    
    def load_context(self, agent_type: str) -> str:
        """Carica il context completo per un agente"""
        context_parts = []
        
        # Context base
        base_context = self.load_prompt(agent_type, "_context.md")
        if base_context:
            context_parts.append(base_context)
        
        # Role principale
        role = self.load_prompt(agent_type, "agent.system.main.role.md")
        if role:
            context_parts.append(role)
        
        # Environment
        environment = self.load_prompt(agent_type, "agent.system.main.environment.md")
        if environment:
            context_parts.append(environment)
        
        # Communication
        communication = self.load_prompt(agent_type, "agent.system.main.communication.md")
        if communication:
            context_parts.append(communication)
        
        # Behaviour
        behaviour = self.load_prompt(agent_type, "agent.system.behaviour.md")
        if behaviour:
            context_parts.append(behaviour)
        
        return "\n\n".join(context_parts)
    
    def build_prompt(self, agent_type: str, task: str, context_data: Dict[str, Any] = None) -> str:
        """Costruisce un prompt completo per un task"""
        context = self.load_context(agent_type)
        
        # Aggiungi dati di contesto se forniti
        if context_data:
            context_vars = "\n".join([f"- {k}: {v}" for k, v in context_data.items()])
            context += f"\n\n## Context Data\n{context_vars}"
        
        # Costruisci prompt finale
        prompt = f"""
{context}

## Current Task
{task}

## Instructions
Analyze the task and provide a detailed response following your role specifications.
Focus on practical, actionable solutions for penetration testing and security analysis.
"""
        
        return prompt.strip()

# Istanze globali
ai_client = AIClient()
prompt_manager = PromptManager()
