#!/usr/bin/env python3
"""
Kali Linux Executor Tool
Tool per eseguire comandi nella shell di Kali Linux e interagire con tools di penetration testing
"""

import subprocess
import logging
import time
import os
import json
import re
from typing import Dict, Any, List, Optional
try:
    from crewai_tools import BaseTool
except ImportError:
    try:
        from crewai.tools import BaseTool
    except ImportError:
        # Fallback per versioni diverse di CrewAI
        class BaseTool:
            def __init__(self, **kwargs):
                for key, value in kwargs.items():
                    setattr(self, key, value)


class KaliExecutorTool(BaseTool):
    """Tool per eseguire comandi nella shell di Kali Linux"""
    
    name: str = "kali_executor"
    description: str = """
    Tool per eseguire comandi nella shell di Kali Linux tramite container Docker e utilizzare tools di penetration testing.
    Supporta:
    - Esecuzione comandi shell sicuri nel container Docker 'elegant_tu'
    - Utilizzo tools Kali (nmap, nikto, sqlmap, metasploit, etc.)
    - Parsing output e estrazione informazioni
    - Gestione timeout e sicurezza
    - Logging dettagliato delle operazioni
    - Verifica automatica dello stato del container Docker

    IMPORTANTE:
    - Esegue solo comandi autorizzati e sicuri per penetration testing
    - Richiede che il container Docker 'elegant_tu' sia in esecuzione
    - Tutti i comandi vengono eseguiti tramite 'docker exec elegant_tu /bin/bash'
    """
    
    def __init__(self):
        super().__init__()
        self._logger = logging.getLogger("heka.kali_executor")
        self._allowed_commands = self._get_allowed_commands()
        self._command_history = []
        self._max_history = 1000
        self._docker_container = "elegant_tu"

        # Verifica che il container Docker sia in esecuzione
        if not self._check_docker_container():
            self._logger.warning(f"Container Docker '{self._docker_container}' non trovato o non in esecuzione")

    def _check_docker_container(self) -> bool:
        """Verifica se il container Docker è in esecuzione"""
        try:
            result = subprocess.run(
                f"docker ps --filter name={self._docker_container} --format '{{{{.Names}}}}'",
                shell=True,
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0 and self._docker_container in result.stdout:
                self._logger.info(f"Container Docker '{self._docker_container}' è in esecuzione")
                return True
            else:
                self._logger.error(f"Container Docker '{self._docker_container}' non è in esecuzione")
                return False

        except Exception as e:
            self._logger.error(f"Errore verifica container Docker: {e}")
            return False

    def _get_allowed_commands(self) -> List[str]:
        """Restituisce lista comandi autorizzati"""
        return [
            # Network scanning
            'nmap', 'masscan', 'zmap', 'unicornscan',
            # Web application testing
            'nikto', 'dirb', 'gobuster', 'wfuzz', 'sqlmap', 'burpsuite',
            # Vulnerability scanning
            'openvas', 'nessus', 'nuclei', 'whatweb',
            # Exploitation frameworks
            'msfconsole', 'msfvenom', 'searchsploit',
            # Network tools
            'netcat', 'nc', 'socat', 'tcpdump', 'wireshark',
            # DNS tools
            'dig', 'nslookup', 'host', 'dnsrecon', 'fierce',
            # SSL/TLS tools
            'sslscan', 'sslyze', 'testssl',
            # Information gathering
            'whois', 'theharvester', 'recon-ng', 'maltego',
            # Password attacks
            'hydra', 'medusa', 'john', 'hashcat',
            # Wireless
            'aircrack-ng', 'airodump-ng', 'aireplay-ng',
            # Basic system commands
            'ls', 'cat', 'grep', 'awk', 'sed', 'sort', 'uniq', 'head', 'tail',
            'ping', 'curl', 'wget', 'telnet', 'ssh'
        ]
    
    def _run(self, command: str, target: str = "", timeout: int = 300, **kwargs) -> str:
        """
        Esegue un comando nella shell di Kali Linux tramite Docker

        Args:
            command: Comando da eseguire
            target: Target del comando (IP, dominio, etc.)
            timeout: Timeout in secondi
            **kwargs: Parametri aggiuntivi

        Returns:
            Output del comando o messaggio di errore
        """
        try:
            # Verifica che il container Docker sia disponibile
            if not self._check_docker_container():
                return f"Errore: Container Docker '{self._docker_container}' non è in esecuzione. Avviare il container prima di eseguire comandi."

            # Valida e prepara il comando
            safe_command = self._validate_and_prepare_command(command, target, **kwargs)
            if not safe_command:
                return "Errore: Comando non autorizzato o non sicuro"

            self._logger.info(f"Esecuzione comando nel container Docker: {safe_command}")

            # Esegui il comando nel container Docker
            result = self._execute_command(safe_command, timeout)

            # Salva nella cronologia
            self._save_to_history(safe_command, result, target)

            # Processa e restituisce il risultato
            return self._process_output(result, command)

        except Exception as e:
            self._logger.error(f"Errore esecuzione comando: {e}")
            return f"Errore: {str(e)}"
    
    def _validate_and_prepare_command(self, command: str, target: str, **kwargs) -> Optional[str]:
        """Valida e prepara il comando per l'esecuzione sicura"""
        try:
            # Estrai il comando base
            cmd_parts = command.strip().split()
            if not cmd_parts:
                return None
                
            base_cmd = cmd_parts[0]
            
            # Verifica se il comando è autorizzato
            if base_cmd not in self._allowed_commands:
                self._logger.warning(f"Comando non autorizzato: {base_cmd}")
                return None
            
            # Prepara comandi specifici
            if base_cmd == 'nmap':
                return self._prepare_nmap_command(command, target, **kwargs)
            elif base_cmd == 'nikto':
                return self._prepare_nikto_command(command, target, **kwargs)
            elif base_cmd == 'sqlmap':
                return self._prepare_sqlmap_command(command, target, **kwargs)
            elif base_cmd == 'gobuster':
                return self._prepare_gobuster_command(command, target, **kwargs)
            elif base_cmd in ['ping', 'curl', 'wget']:
                return self._prepare_network_command(command, target, **kwargs)
            else:
                # Per altri comandi, usa il comando così com'è ma con validazioni
                return self._sanitize_command(command, target)
                
        except Exception as e:
            self._logger.error(f"Errore preparazione comando: {e}")
            return None
    
    def _prepare_nmap_command(self, command: str, target: str, **kwargs) -> str:
        """Prepara comando nmap sicuro"""
        if not target:
            return None
            
        # Comando nmap base sicuro
        safe_cmd = f"nmap -sS -sV -O --script=default,vuln {target}"
        
        # Aggiungi opzioni specifiche se richieste
        if 'ports' in kwargs:
            safe_cmd += f" -p {kwargs['ports']}"
        if 'fast' in kwargs and kwargs['fast']:
            safe_cmd += " -T4 -F"
        if 'stealth' in kwargs and kwargs['stealth']:
            safe_cmd = safe_cmd.replace('-sS', '-sS -f')
            
        return safe_cmd
    
    def _prepare_nikto_command(self, command: str, target: str, **kwargs) -> str:
        """Prepara comando nikto sicuro"""
        if not target:
            return None
            
        safe_cmd = f"nikto -h {target}"
        
        if 'port' in kwargs:
            safe_cmd += f" -p {kwargs['port']}"
        if 'ssl' in kwargs and kwargs['ssl']:
            safe_cmd += " -ssl"
            
        return safe_cmd
    
    def _prepare_sqlmap_command(self, command: str, target: str, **kwargs) -> str:
        """Prepara comando sqlmap sicuro"""
        if not target:
            return None
            
        safe_cmd = f"sqlmap -u '{target}' --batch --level=1 --risk=1"
        
        if 'data' in kwargs:
            safe_cmd += f" --data='{kwargs['data']}'"
        if 'cookie' in kwargs:
            safe_cmd += f" --cookie='{kwargs['cookie']}'"
            
        return safe_cmd
    
    def _prepare_gobuster_command(self, command: str, target: str, **kwargs) -> str:
        """Prepara comando gobuster sicuro"""
        if not target:
            return None
            
        wordlist = kwargs.get('wordlist', '/usr/share/wordlists/dirb/common.txt')
        safe_cmd = f"gobuster dir -u {target} -w {wordlist}"
        
        if 'extensions' in kwargs:
            safe_cmd += f" -x {kwargs['extensions']}"
        if 'threads' in kwargs:
            safe_cmd += f" -t {kwargs['threads']}"
            
        return safe_cmd
    
    def _prepare_network_command(self, command: str, target: str, **kwargs) -> str:
        """Prepara comandi di rete sicuri"""
        if not target:
            return None
            
        cmd_parts = command.split()
        base_cmd = cmd_parts[0]
        
        if base_cmd == 'ping':
            return f"ping -c 4 {target}"
        elif base_cmd == 'curl':
            return f"curl -I -L --max-time 30 {target}"
        elif base_cmd == 'wget':
            return f"wget --spider --timeout=30 {target}"
            
        return None
    
    def _sanitize_command(self, command: str, target: str) -> str:
        """Sanitizza comando generico"""
        # Rimuovi caratteri pericolosi
        dangerous_chars = [';', '&&', '||', '|', '>', '<', '`', '$', '(', ')']
        sanitized = command
        
        for char in dangerous_chars:
            if char in sanitized and char not in ['>', '<']:  # Permetti redirect semplici
                self._logger.warning(f"Carattere pericoloso rimosso: {char}")
                sanitized = sanitized.replace(char, '')
        
        # Sostituisci placeholder target
        if target and '{target}' in sanitized:
            sanitized = sanitized.replace('{target}', target)
        elif target and not any(target in part for part in sanitized.split()):
            sanitized += f" {target}"
            
        return sanitized
    
    def _execute_command(self, command: str, timeout: int) -> Dict[str, Any]:
        """Esegue il comando nel container Docker Kali e restituisce il risultato"""
        start_time = time.time()

        try:
            # Escape delle virgolette nel comando per Docker
            escaped_command = command.replace('"', '\\"').replace('`', '\\`').replace('$', '\\$')

            # Prepara il comando Docker per eseguire il comando nel container Kali
            docker_command = f'docker exec {self._docker_container} /bin/bash -c "{escaped_command}"'

            self._logger.info(f"Esecuzione comando Docker: {docker_command}")

            # Esegui il comando nel container Docker
            process = subprocess.Popen(
                docker_command,
                shell=True,
                stdout=subprocess.PIPE,
                stderr=subprocess.PIPE,
                text=True,
                timeout=timeout
            )

            stdout, stderr = process.communicate()
            end_time = time.time()

            return {
                'command': command,
                'docker_command': docker_command,
                'return_code': process.returncode,
                'stdout': stdout,
                'stderr': stderr,
                'execution_time': end_time - start_time,
                'timestamp': time.time()
            }

        except subprocess.TimeoutExpired:
            process.kill()
            return {
                'command': command,
                'return_code': -1,
                'stdout': '',
                'stderr': f'Comando interrotto per timeout ({timeout}s)',
                'execution_time': timeout,
                'timestamp': time.time()
            }
        except Exception as e:
            return {
                'command': command,
                'return_code': -1,
                'stdout': '',
                'stderr': str(e),
                'execution_time': 0,
                'timestamp': time.time()
            }
    
    def _process_output(self, result: Dict[str, Any], original_command: str) -> str:
        """Processa l'output del comando e estrae informazioni utili"""
        if result['return_code'] != 0:
            return f"Errore esecuzione comando:\n{result['stderr']}"
        
        output = result['stdout']
        
        # Processa output specifici per comando
        if 'nmap' in original_command:
            return self._process_nmap_output(output)
        elif 'nikto' in original_command:
            return self._process_nikto_output(output)
        elif 'sqlmap' in original_command:
            return self._process_sqlmap_output(output)
        elif 'gobuster' in original_command:
            return self._process_gobuster_output(output)
        else:
            return self._process_generic_output(output)
    
    def _process_nmap_output(self, output: str) -> str:
        """Processa output di nmap"""
        lines = output.split('\n')
        processed = []
        
        for line in lines:
            if 'open' in line or 'filtered' in line or 'closed' in line:
                processed.append(f"🔍 {line.strip()}")
            elif 'OS:' in line:
                processed.append(f"💻 {line.strip()}")
            elif 'Service Info:' in line:
                processed.append(f"🔧 {line.strip()}")
        
        if processed:
            return "Risultati Nmap:\n" + "\n".join(processed)
        else:
            return output[:2000]  # Limita output
    
    def _process_nikto_output(self, output: str) -> str:
        """Processa output di nikto"""
        lines = output.split('\n')
        vulnerabilities = []
        
        for line in lines:
            if '+ OSVDB-' in line or '+ CVE-' in line:
                vulnerabilities.append(f"⚠️  {line.strip()}")
        
        if vulnerabilities:
            return "Vulnerabilità trovate da Nikto:\n" + "\n".join(vulnerabilities[:10])
        else:
            return output[:2000]
    
    def _process_sqlmap_output(self, output: str) -> str:
        """Processa output di sqlmap"""
        if 'vulnerable' in output.lower():
            return "🚨 SQL Injection vulnerabilità rilevata!\n" + output[:1500]
        elif 'not vulnerable' in output.lower():
            return "✅ Nessuna SQL Injection rilevata"
        else:
            return output[:2000]
    
    def _process_gobuster_output(self, output: str) -> str:
        """Processa output di gobuster"""
        lines = output.split('\n')
        found_dirs = []
        
        for line in lines:
            if '(Status:' in line and '200' in line:
                found_dirs.append(f"📁 {line.strip()}")
        
        if found_dirs:
            return "Directory trovate:\n" + "\n".join(found_dirs[:20])
        else:
            return output[:2000]
    
    def _process_generic_output(self, output: str) -> str:
        """Processa output generico"""
        return output[:2000] if len(output) > 2000 else output
    
    def _save_to_history(self, command: str, result: Dict[str, Any], target: str):
        """Salva comando nella cronologia"""
        history_entry = {
            'timestamp': time.time(),
            'command': command,
            'target': target,
            'return_code': result['return_code'],
            'execution_time': result['execution_time'],
            'success': result['return_code'] == 0
        }
        
        self._command_history.append(history_entry)
        
        # Mantieni solo gli ultimi N comandi
        if len(self._command_history) > self._max_history:
            self._command_history = self._command_history[-self._max_history:]
    
    def get_command_history(self) -> List[Dict[str, Any]]:
        """Restituisce la cronologia dei comandi"""
        return self._command_history.copy()
    
    def get_available_tools(self) -> List[str]:
        """Restituisce lista tools disponibili"""
        return self._allowed_commands.copy()

    def get_docker_status(self) -> Dict[str, Any]:
        """Restituisce lo stato del container Docker"""
        try:
            # Verifica se il container esiste
            result = subprocess.run(
                f"docker ps -a --filter name={self._docker_container} --format 'table {{{{.Names}}}}\\t{{{{.Status}}}}\\t{{{{.Image}}}}'",
                shell=True,
                capture_output=True,
                text=True,
                timeout=10
            )

            if result.returncode == 0:
                lines = result.stdout.strip().split('\n')
                if len(lines) > 1:  # Header + almeno una riga di dati
                    container_info = lines[1].split('\t')
                    return {
                        'container_name': self._docker_container,
                        'exists': True,
                        'running': self._check_docker_container(),
                        'status': container_info[1] if len(container_info) > 1 else 'Unknown',
                        'image': container_info[2] if len(container_info) > 2 else 'Unknown'
                    }
                else:
                    return {
                        'container_name': self._docker_container,
                        'exists': False,
                        'running': False,
                        'status': 'Not found',
                        'image': 'N/A'
                    }
            else:
                return {
                    'container_name': self._docker_container,
                    'exists': False,
                    'running': False,
                    'status': 'Error checking status',
                    'image': 'N/A',
                    'error': result.stderr
                }

        except Exception as e:
            return {
                'container_name': self._docker_container,
                'exists': False,
                'running': False,
                'status': 'Error',
                'image': 'N/A',
                'error': str(e)
            }
