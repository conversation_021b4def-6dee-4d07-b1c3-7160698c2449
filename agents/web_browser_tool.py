"""
Web Browser Tool utilizzando Playwright per navigazione web intelligente
Supporta gli agenti con ricerche mirate e raccolta informazioni
"""

import asyncio
import json
import logging
import time
from typing import Dict, List, Any, Optional
from urllib.parse import urljoin, urlparse
from crewai.tools import BaseTool
from pydantic import BaseModel, Field

try:
    from playwright.async_api import async_playwright, <PERSON>rows<PERSON>, Page
    PLAYWRIGHT_AVAILABLE = True
except ImportError:
    PLAYWRIGHT_AVAILABLE = False
    print("⚠️ Playwright non disponibile. Installare con: pip install playwright && playwright install")


class WebSearchInput(BaseModel):
    """Input per ricerca web"""
    query: str = Field(..., description="Query di ricerca")
    max_results: int = Field(default=5, description="Numero massimo di risultati")
    search_engine: str = Field(default="duckduckgo", description="Motore di ricerca da utilizzare")


class WebNavigateInput(BaseModel):
    """Input per navigazione web"""
    url: str = Field(..., description="URL da visitare")
    extract_links: bool = Field(default=True, description="Estrai link dalla pagina")
    extract_text: bool = Field(default=True, description="Estrai testo dalla pagina")
    screenshot: bool = Field(default=False, description="Cattura screenshot")


class WebAnalyzeInput(BaseModel):
    """Input per analisi pagina web"""
    url: str = Field(..., description="URL da analizzare")
    look_for: str = Field(..., description="Cosa cercare nella pagina")
    deep_analysis: bool = Field(default=False, description="Analisi approfondita")


class WebBrowserTool(BaseTool):
    """Tool per navigazione web con Playwright"""

    name: str = "web_browser"
    description: str = """
    Strumento per navigazione web intelligente che supporta:
    - Ricerca web su motori di ricerca
    - Navigazione e analisi di pagine web
    - Estrazione di informazioni specifiche
    - Raccolta di intelligence per supportare altri agenti

    Utilizza Playwright per navigazione avanzata e JavaScript rendering.
    """

    def __init__(self):
        super().__init__()
        # Usa variabili di classe invece di attributi di istanza
        self._logger = logging.getLogger("heka.web_browser_tool")
        self._browser: Optional[Browser] = None
        self._page: Optional[Page] = None
        self._session_pages_visited = 0
        self._max_pages_per_session = 10
        
    async def _ensure_browser(self):
        """Assicura che il browser sia avviato"""
        if not PLAYWRIGHT_AVAILABLE:
            raise Exception("Playwright non disponibile. Installare con: pip install playwright && playwright install")
            
        if not self._browser:
            playwright = await async_playwright().start()
            self._browser = await playwright.chromium.launch(
                headless=True,
                args=['--no-sandbox', '--disable-dev-shm-usage']
            )

        if not self._page:
            self._page = await self._browser.new_page()
            await self._page.set_extra_http_headers({
                'User-Agent': "Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36"
            })
    
    async def _close_browser(self):
        """Chiude il browser"""
        if self._page:
            await self._page.close()
            self._page = None
        if self._browser:
            await self._browser.close()
            self._browser = None
    
    def _run(self, query: str, action: str = "search", **kwargs) -> str:
        """Esegue l'azione richiesta"""
        try:
            return asyncio.run(self._async_run(query, action, **kwargs))
        except Exception as e:
            self._logger.error(f"Errore web browser tool: {e}")
            return f"Errore: {str(e)}"
    
    async def _async_run(self, query: str, action: str = "search", **kwargs) -> str:
        """Esecuzione asincrona dell'azione"""
        try:
            await self._ensure_browser()
            
            if action == "search":
                return await self._web_search(query, **kwargs)
            elif action == "navigate":
                return await self._web_navigate(query, **kwargs)
            elif action == "analyze":
                return await self._web_analyze(query, **kwargs)
            else:
                return f"Azione non supportata: {action}"
                
        except Exception as e:
            self._logger.error(f"Errore in _async_run: {e}")
            return f"Errore: {str(e)}"
        finally:
            # Chiudi browser se troppi pagine visitate
            if self._session_pages_visited > self._max_pages_per_session:
                await self._close_browser()
                self._session_pages_visited = 0
    
    async def _web_search(self, query: str, max_results: int = 5, search_engine: str = "duckduckgo") -> str:
        """Esegue ricerca web"""
        try:
            self._session_pages_visited += 1
            
            if search_engine.lower() == "duckduckgo":
                search_url = f"https://duckduckgo.com/?q={query.replace(' ', '+')}"
            else:
                search_url = f"https://www.google.com/search?q={query.replace(' ', '+')}"
            
            await self._page.goto(search_url, wait_until="networkidle")
            await self._page.wait_for_timeout(2000)  # Attendi caricamento

            # Estrai risultati di ricerca
            results = []

            if search_engine.lower() == "duckduckgo":
                # Selettori per DuckDuckGo
                result_elements = await self._page.query_selector_all('[data-result="result"]')
            else:
                # Selettori per Google
                result_elements = await self._page.query_selector_all('div.g')
            
            for i, element in enumerate(result_elements[:max_results]):
                try:
                    title_elem = await element.query_selector('h3, .result__title')
                    link_elem = await element.query_selector('a')
                    snippet_elem = await element.query_selector('.result__snippet, .VwiC3b')
                    
                    if title_elem and link_elem:
                        title = await title_elem.inner_text()
                        link = await link_elem.get_attribute('href')
                        snippet = await snippet_elem.inner_text() if snippet_elem else ""
                        
                        results.append({
                            "title": title.strip(),
                            "url": link,
                            "snippet": snippet.strip()
                        })
                except Exception as e:
                    self._logger.warning(f"Errore estrazione risultato {i}: {e}")
                    continue
            
            return json.dumps({
                "query": query,
                "search_engine": search_engine,
                "results_count": len(results),
                "results": results
            }, indent=2)
            
        except Exception as e:
            return f"Errore ricerca web: {str(e)}"
    
    async def _web_navigate(self, url: str, extract_links: bool = True, extract_text: bool = True, screenshot: bool = False) -> str:
        """Naviga verso un URL e estrae informazioni"""
        try:
            self._session_pages_visited += 1

            await self._page.goto(url, wait_until="networkidle")
            await self._page.wait_for_timeout(3000)

            result = {
                "url": url,
                "title": await self._page.title(),
                "timestamp": time.time()
            }
            
            if extract_text:
                # Estrai testo principale
                body_text = await self._page.evaluate("""
                    () => {
                        // Rimuovi script e style
                        const scripts = document.querySelectorAll('script, style');
                        scripts.forEach(el => el.remove());

                        // Estrai testo dal body
                        return document.body ? document.body.innerText : '';
                    }
                """)
                result["text"] = body_text[:5000]  # Limita a 5000 caratteri

            if extract_links:
                # Estrai link
                links = await self._page.evaluate("""
                    () => {
                        const links = Array.from(document.querySelectorAll('a[href]'));
                        return links.map(link => ({
                            text: link.innerText.trim(),
                            href: link.href,
                            title: link.title || ''
                        })).filter(link => link.text && link.href);
                    }
                """)
                result["links"] = links[:20]  # Limita a 20 link

            if screenshot:
                # Cattura screenshot (base64)
                screenshot_bytes = await self._page.screenshot()
                result["screenshot_size"] = len(screenshot_bytes)
                result["screenshot_available"] = True
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            return f"Errore navigazione: {str(e)}"
    
    async def _web_analyze(self, url: str, look_for: str, deep_analysis: bool = False) -> str:
        """Analizza una pagina web cercando informazioni specifiche"""
        try:
            self._session_pages_visited += 1

            await self._page.goto(url, wait_until="networkidle")
            await self._page.wait_for_timeout(3000)

            # Estrai tutto il testo
            page_text = await self._page.evaluate("""
                () => {
                    const scripts = document.querySelectorAll('script, style');
                    scripts.forEach(el => el.remove());
                    return document.body ? document.body.innerText : '';
                }
            """)
            
            # Cerca informazioni specifiche
            search_terms = look_for.lower().split()
            relevant_sections = []
            
            lines = page_text.split('\n')
            for i, line in enumerate(lines):
                line_lower = line.lower()
                if any(term in line_lower for term in search_terms):
                    # Aggiungi contesto (linee precedenti e successive)
                    start = max(0, i-2)
                    end = min(len(lines), i+3)
                    context = '\n'.join(lines[start:end])
                    relevant_sections.append({
                        "line_number": i,
                        "content": context.strip(),
                        "relevance_score": sum(1 for term in search_terms if term in line_lower)
                    })
            
            # Ordina per rilevanza
            relevant_sections.sort(key=lambda x: x["relevance_score"], reverse=True)
            
            result = {
                "url": url,
                "search_query": look_for,
                "page_title": await self._page.title(),
                "relevant_sections_found": len(relevant_sections),
                "top_matches": relevant_sections[:10],  # Top 10 match
                "analysis_timestamp": time.time()
            }

            if deep_analysis:
                # Analisi più approfondita
                result["page_structure"] = await self._page.evaluate("""
                    () => {
                        return {
                            headings: Array.from(document.querySelectorAll('h1,h2,h3,h4,h5,h6')).map(h => h.innerText.trim()),
                            forms: Array.from(document.querySelectorAll('form')).length,
                            images: Array.from(document.querySelectorAll('img')).length,
                            external_links: Array.from(document.querySelectorAll('a[href^="http"]')).length
                        }
                    }
                """)
            
            return json.dumps(result, indent=2)
            
        except Exception as e:
            return f"Errore analisi: {str(e)}"
