"""
Custom LLM wrapper per CrewAI che utilizza ai_client.py
Integra il sistema AI esistente con CrewAI
"""

import asyncio
import threading
from typing import Any, Dict, List, Optional
from crewai.llm import LLM
from .ai_client import ai_client, prompt_manager, AIModel, AIRequest


class HekaLLM(LLM):
    """
    Custom LLM wrapper per CrewAI che utilizza il sistema AI di Heka
    """

    def __init__(self, model: AIModel = AIModel.QWEN3_BRAIN, agent_type: str = "hacker"):
        # Passa il nome del modello come stringa al costruttore LLM
        super().__init__(model=f"heka_{model.value}_{agent_type}")
        self.ai_model = model
        self.agent_type = agent_type
        self._ensure_client_started()
    
    def _ensure_client_started(self):
        """Assicura che il client AI sia avviato"""
        try:
            # Verifica se il client ha già una sessione attiva
            if ai_client.session is None:
                # Avvia il client in modo sicuro
                try:
                    loop = asyncio.get_event_loop()
                    if loop.is_running():
                        # Se siamo già in un loop, crea un task
                        asyncio.create_task(ai_client.start())
                    else:
                        # Se non siamo in un loop, eseguilo
                        loop.run_until_complete(ai_client.start())
                except RuntimeError:
                    # Se non c'è un loop, creane uno nuovo
                    asyncio.run(ai_client.start())
        except Exception as e:
            print(f"Errore avvio client AI: {e}")
    
    def call(self, messages: List[Dict[str, Any]], **kwargs) -> str:
        """
        Metodo principale chiamato da CrewAI per ottenere risposte
        """
        try:
            # Estrai il prompt dall'ultimo messaggio
            if not messages:
                return "Nessun messaggio fornito"

            # Gestisci diversi formati di input
            if isinstance(messages, str):
                prompt = messages
                context = ""
            elif isinstance(messages, list) and len(messages) > 0:
                last_message = messages[-1]
                if isinstance(last_message, dict):
                    prompt = last_message.get('content', str(last_message))
                else:
                    prompt = str(last_message)

                # Costruisci il context dai messaggi precedenti
                context_parts = []
                for msg in messages[:-1]:
                    if isinstance(msg, dict):
                        role = msg.get('role', 'user')
                        content = msg.get('content', str(msg))
                        context_parts.append(f"{role}: {content}")
                    else:
                        context_parts.append(str(msg))

                context = "\n".join(context_parts) if context_parts else ""
            else:
                prompt = str(messages)
                context = ""

            # Carica context specifico dell'agente
            agent_context = prompt_manager.load_context(self.agent_type)
            if agent_context:
                context = f"{agent_context}\n\n{context}" if context else agent_context

            # Esegui la richiesta AI in modo thread-safe
            return self._run_async_call(prompt, context)

        except Exception as e:
            return f"Errore nella chiamata LLM: {str(e)}"
    
    def _run_async_call(self, prompt: str, context: str) -> str:
        """Esegue la chiamata in modo completamente sincrono"""
        try:
            # Evita completamente i loop asincroni - usa solo chiamate sincrone
            return self._sync_call(prompt, context)
        except Exception as e:
            return f"Errore nell'esecuzione della chiamata: {str(e)}"

    def _sync_call(self, prompt: str, context: str) -> str:
        """Chiamata completamente sincrona"""
        try:
            # Prova prima a usare le API reali se configurate
            api_url = ai_client.api_urls.get(self.ai_model)
            if api_url and api_url.strip():
                try:
                    # Usa le API reali con requests (sincrono)
                    return self._real_api_call_sync(prompt, context)
                except Exception as e:
                    print(f"API reale fallita, uso simulazione: {e}")

            # Fallback a simulazione
            return self._simulation_response(prompt, context)

        except Exception as e:
            return f"Errore nella chiamata sincrona: {str(e)}"

    def _real_api_call_sync(self, prompt: str, context: str) -> str:
        """Chiamata sincrona alle API reali usando requests"""
        import requests

        try:
            # Ottieni URL per il modello
            api_url = ai_client.api_urls.get(self.ai_model)
            if not api_url:
                raise ValueError(f"URL API non configurato per {self.ai_model.value}")

            # Prepara payload nel formato corretto
            # Limita la lunghezza per evitare errori di nome file troppo lungo
            max_prompt_length = 200
            if len(prompt) > max_prompt_length:
                prompt = prompt[:max_prompt_length] + "..."

            payload = {"text": prompt}

            # Invia richiesta POST con requests (sincrono)
            response = requests.post(
                api_url,
                json=payload,
                headers={'Content-Type': 'application/json'},
                timeout=30
            )

            if response.status_code == 200:
                data = response.json()

                # Estrai contenuto dalla risposta
                content = data.get("response", "")
                if not content:
                    content = data.get("data", "")

                return content if content else "Risposta vuota dall'API"
            else:
                raise Exception(f"HTTP {response.status_code}: {response.text[:200]}")

        except Exception as e:
            raise Exception(f"Errore API reale: {str(e)}")

    def _simulation_response(self, prompt: str, context: str) -> str:
        """Genera una risposta simulata"""

        try:
            # Simula una risposta basata sul tipo di agente
            if self.ai_model == AIModel.QWEN3_CODER:
                response = f"""
# Analisi di sicurezza - {self.agent_type}

## Prompt ricevuto:
{prompt[:200]}...

## Context:
{context[:200] if context else 'Nessun context fornito'}...

## Risposta simulata per QWEN3_CODER:
Questa è una risposta simulata per il modello QWEN3_CODER.
Il sistema è configurato correttamente e pronto per l'uso.

### Raccomandazioni:
1. Configurare le API URL reali in .env
2. Testare la connettività con i modelli AI
3. Verificare i permessi di accesso

### Codice di esempio:
```python
# Esempio di utilizzo
result = ai_client.code_completion(prompt, context)
print(result)
```
"""
            else:
                response = f"""
# Analisi di sicurezza - {self.agent_type}

## Prompt ricevuto:
{prompt[:200]}...

## Context:
{context[:200] if context else 'Nessun context fornito'}...

## Risposta simulata per QWEN3_BRAIN:
Questa è una risposta simulata per il modello QWEN3_BRAIN.
Il sistema multi-agent è operativo e pronto per l'uso.

### Analisi:
- Sistema configurato correttamente
- Agenti disponibili e funzionanti
- Strumenti di penetration testing pronti

### Prossimi passi:
1. Configurare le API URL reali
2. Testare su ambiente autorizzato
3. Personalizzare i prompt per le esigenze specifiche
"""

            return response.strip()

        except Exception as e:
            return f"Errore nella simulazione: {str(e)}"


    
    @property
    def model_name(self) -> str:
        """Nome del modello per CrewAI"""
        return f"heka_{self.ai_model.value}_{self.agent_type}"


class ReconnaissanceLLM(HekaLLM):
    """LLM specializzato per reconnaissance"""
    
    def __init__(self):
        super().__init__(model=AIModel.QWEN3_BRAIN, agent_type="hacker")


class ExploitationLLM(HekaLLM):
    """LLM specializzato per exploitation"""
    
    def __init__(self):
        super().__init__(model=AIModel.QWEN3_CODER, agent_type="hacker")


class LinuxAdminLLM(HekaLLM):
    """LLM specializzato per amministrazione Linux"""
    
    def __init__(self):
        super().__init__(model=AIModel.QWEN3_BRAIN, agent_type="hacker")


class ReportingLLM(HekaLLM):
    """LLM specializzato per reporting"""

    def __init__(self):
        super().__init__(model=AIModel.QWEN3_BRAIN, agent_type="researcher")


class WebIntelligenceLLM(HekaLLM):
    """LLM specializzato per web intelligence e ricerca"""

    def __init__(self):
        super().__init__(model=AIModel.QWEN3_BRAIN, agent_type="researcher")


class KaliExecutorLLM(HekaLLM):
    """LLM specializzato per esecuzione comandi Kali Linux"""

    def __init__(self):
        super().__init__(model=AIModel.QWEN3_BRAIN, agent_type="executor")
