#!/usr/bin/env python3
"""
Collaborative AI Crew - Sistema di Agenti AI Collaborativi per Penetration Testing
Architettura ristrutturata per comunicazione continua e obiettivi dinamici

Caratteristiche:
- Comunicazione inter-agent continua
- Raccolta informazioni dinamica su test necessari
- Web search con dorks personalizzate
- Esecuzione comandi guidata da AI
- Analisi collaborativa degli output
- Ciclo continuo fino al raggiungimento dell'obiettivo
"""

import asyncio
import json
import logging
import time
from datetime import datetime
from typing import Dict, List, Any, Optional, Tuple
from dataclasses import dataclass, asdict

from crewai import Agent, Task, Crew, Process
from crewai.tools import BaseTool

from .crew_ai_llm import (
    ReconnaissanceLLM, ExploitationLLM, LinuxAdminLLM, 
    ReportingLLM, WebIntelligenceLLM, KaliExecutorLLM
)
from .ai_client import prompt_manager, ai_client
from .config_manager import config_manager
from .web_browser_tool import WebBrowserTool
from .kali_executor_tool import KaliExecutorTool
from database.pentest_database import pentest_db


@dataclass
class AgentMessage:
    """Messaggio tra agenti"""
    from_agent: str
    to_agent: str
    message_type: str  # request, response, info, alert, command, result
    content: str
    timestamp: float
    session_id: int
    metadata: Dict[str, Any] = None

    def to_dict(self) -> Dict[str, Any]:
        return asdict(self)


@dataclass
class ObjectiveStatus:
    """Stato dell'obiettivo"""
    target: str
    objective: str
    status: str  # active, completed, failed
    progress: float  # 0.0 - 1.0
    findings: List[Dict[str, Any]]
    next_actions: List[str]
    completion_criteria: List[str]
    timestamp: float


class CollaborativeAgent:
    """Agente collaborativo con capacità di comunicazione"""
    
    def __init__(self, agent: Agent, agent_name: str, specialization: str):
        self.agent = agent
        self.name = agent_name
        self.specialization = specialization
        self.knowledge_base = []
        self.active_tasks = []
        self.message_queue = []
        self.logger = logging.getLogger(f"heka.agent.{agent_name}")
    
    def add_knowledge(self, knowledge: Dict[str, Any]):
        """Aggiunge conoscenza alla base di conoscenza dell'agente"""
        knowledge['timestamp'] = time.time()
        self.knowledge_base.append(knowledge)
        self.logger.info(f"Aggiunta conoscenza: {knowledge.get('type', 'unknown')}")
    
    def get_relevant_knowledge(self, topic: str) -> List[Dict[str, Any]]:
        """Recupera conoscenza rilevante per un topic"""
        relevant = []
        for knowledge in self.knowledge_base:
            if topic.lower() in str(knowledge).lower():
                relevant.append(knowledge)
        return relevant[-10:]  # Ultimi 10 elementi rilevanti
    
    def receive_message(self, message: AgentMessage):
        """Riceve un messaggio da un altro agente"""
        self.message_queue.append(message)
        self.logger.info(f"Ricevuto messaggio da {message.from_agent}: {message.message_type}")
    
    def get_pending_messages(self) -> List[AgentMessage]:
        """Recupera messaggi in attesa"""
        messages = self.message_queue.copy()
        self.message_queue.clear()
        return messages


class CollaborativeCrew:
    """
    Crew di agenti collaborativi per penetration testing continuo
    """
    
    def __init__(self):
        self.logger = logging.getLogger("heka.collaborative_crew")
        self.tools = self._setup_tools()
        self.agents = self._setup_collaborative_agents()
        self.message_bus = []
        self.session_id = None
        self.objective_status = None
        self.max_iterations = 50
        self.iteration_count = 0
        
    def _setup_tools(self) -> Dict[str, BaseTool]:
        """Configura gli strumenti disponibili"""
        return {
            'web_browser': WebBrowserTool(),
            'kali_executor': KaliExecutorTool()
        }
    
    def _setup_collaborative_agents(self) -> Dict[str, CollaborativeAgent]:
        """Configura gli agenti collaborativi"""
        
        # Carica configurazioni
        recon_config = config_manager.get_agent_config('reconnaissance')
        exploit_config = config_manager.get_agent_config('exploitation')
        web_intel_config = config_manager.get_agent_config('web_intelligence')
        kali_config = config_manager.get_agent_config('kali_executor')
        
        # Agente Intelligence Gatherer
        intelligence_agent = Agent(
            role="Intelligence Gatherer",
            goal="Raccogliere informazioni su come eseguire test specifici e coordinare la ricerca",
            backstory=prompt_manager.load_prompt("researcher", "agent.system.main.role.md") +
                     "\nSpecializzato nella raccolta di informazioni su metodologie di testing e vulnerabilità.",
            llm=ReconnaissanceLLM(),
            tools=[self.tools['web_browser']],
            verbose=True,
            allow_delegation=True,
            max_iter=10
        )
        
        # Agente Web Search Specialist
        web_search_agent = Agent(
            role="Web Search Specialist", 
            goal="Eseguire ricerche web con dorks personalizzate e testare link specifici",
            backstory=prompt_manager.load_prompt("researcher", "agent.system.main.role.md") +
                     "\nEsperto in Google Dorks, ricerche OSINT e analisi di contenuti web.",
            llm=WebIntelligenceLLM(),
            tools=[self.tools['web_browser']],
            verbose=True,
            allow_delegation=True,
            max_iter=10
        )
        
        # Agente Terminal Executor
        terminal_agent = Agent(
            role="Terminal Executor",
            goal="Eseguire comandi forniti dagli altri agenti e condividere output per analisi",
            backstory=prompt_manager.load_prompt("executor", "agent.system.main.role.md") +
                     "\nSpecializzato nell'esecuzione di comandi Kali Linux e tools di penetration testing.",
            llm=KaliExecutorLLM(),
            tools=[self.tools['kali_executor']],
            verbose=True,
            allow_delegation=False,
            max_iter=10
        )
        
        # Agente Analysis Coordinator
        analysis_agent = Agent(
            role="Analysis Coordinator",
            goal="Analizzare output dei comandi e coordinare le prossime azioni per raggiungere l'obiettivo",
            backstory=prompt_manager.load_prompt("hacker", "agent.system.main.role.md") +
                     "\nEsperto nell'analisi di risultati di penetration testing e pianificazione strategica.",
            llm=ExploitationLLM(),
            tools=[],
            verbose=True,
            allow_delegation=True,
            max_iter=10
        )
        
        return {
            'intelligence': CollaborativeAgent(intelligence_agent, 'intelligence', 'information_gathering'),
            'web_search': CollaborativeAgent(web_search_agent, 'web_search', 'web_intelligence'),
            'terminal': CollaborativeAgent(terminal_agent, 'terminal', 'command_execution'),
            'analysis': CollaborativeAgent(analysis_agent, 'analysis', 'result_analysis')
        }
    
    def send_message(self, from_agent: str, to_agent: str, message_type: str, 
                    content: str, metadata: Dict[str, Any] = None):
        """Invia messaggio tra agenti"""
        message = AgentMessage(
            from_agent=from_agent,
            to_agent=to_agent,
            message_type=message_type,
            content=content,
            timestamp=time.time(),
            session_id=self.session_id,
            metadata=metadata or {}
        )
        
        # Aggiungi al message bus
        self.message_bus.append(message)
        
        # Invia al destinatario
        if to_agent in self.agents:
            self.agents[to_agent].receive_message(message)
        elif to_agent == "all":
            for agent_name, agent in self.agents.items():
                if agent_name != from_agent:
                    agent.receive_message(message)
        
        # Log nel database
        if self.session_id:
            pentest_db.log_agent_communication(
                self.session_id, from_agent, to_agent, message_type, content
            )
        
        self.logger.info(f"Messaggio inviato: {from_agent} -> {to_agent} ({message_type})")
    
    def broadcast_knowledge(self, from_agent: str, knowledge: Dict[str, Any]):
        """Condivide conoscenza con tutti gli agenti"""
        for agent_name, agent in self.agents.items():
            if agent_name != from_agent:
                agent.add_knowledge(knowledge)
        
        self.send_message(
            from_agent, "all", "info",
            f"Nuova conoscenza condivisa: {knowledge.get('type', 'unknown')}",
            {"knowledge": knowledge}
        )

    async def execute_collaborative_pentest(self, target: str, objective: str) -> Dict[str, Any]:
        """
        Esegue penetration test collaborativo continuo
        """
        self.logger.info(f"Avvio penetration test collaborativo su {target}")

        # Inizializza stato obiettivo
        self.objective_status = ObjectiveStatus(
            target=target,
            objective=objective,
            status="active",
            progress=0.0,
            findings=[],
            next_actions=[f"Inizia reconnaissance su {target}"],
            completion_criteria=[
                "Identificare almeno una vulnerabilità sfruttabile",
                "Ottenere accesso al sistema target",
                "Documentare il percorso di compromissione"
            ],
            timestamp=time.time()
        )

        self.iteration_count = 0

        # Messaggio iniziale di coordinamento
        self.send_message(
            "system", "all", "info",
            f"Obiettivo: {objective} su target {target}. Iniziamo la collaborazione!"
        )

        # Ciclo collaborativo continuo
        while (self.objective_status.status == "active" and
               self.iteration_count < self.max_iterations):

            self.iteration_count += 1
            self.logger.info(f"Iterazione {self.iteration_count}/{self.max_iterations}")

            # Fase 1: Intelligence Gathering
            await self._intelligence_phase()

            # Fase 2: Web Search & OSINT
            await self._web_search_phase()

            # Fase 3: Command Execution
            await self._execution_phase()

            # Fase 4: Analysis & Planning
            await self._analysis_phase()

            # Verifica completamento obiettivo
            if await self._check_objective_completion():
                self.objective_status.status = "completed"
                break

            # Pausa tra iterazioni
            await asyncio.sleep(2)

        # Finalizza risultati
        return await self._finalize_results()

    async def _intelligence_phase(self):
        """Fase di raccolta intelligence"""
        self.logger.info("Fase Intelligence Gathering")

        intelligence_agent = self.agents['intelligence']

        # Recupera messaggi pendenti
        messages = intelligence_agent.get_pending_messages()

        # Costruisci contesto
        context = self._build_context_for_agent('intelligence')

        # Crea task di intelligence gathering
        task_description = f"""
        INTELLIGENCE GATHERING PHASE - Iterazione {self.iteration_count}

        Target: {self.objective_status.target}
        Obiettivo: {self.objective_status.objective}
        Progresso attuale: {self.objective_status.progress:.1%}

        Messaggi ricevuti: {len(messages)}
        {self._format_messages(messages)}

        Contesto attuale:
        {context}

        COMPITI:
        1. Analizza le informazioni disponibili sul target
        2. Identifica che tipo di test sono necessari
        3. Cerca informazioni su metodologie specifiche
        4. Coordina con gli altri agenti per le prossime azioni

        Rispondi con:
        - Informazioni raccolte
        - Test raccomandati
        - Richieste specifiche agli altri agenti
        """

        task = Task(
            description=task_description,
            agent=intelligence_agent.agent,
            expected_output="Report di intelligence con raccomandazioni per i prossimi passi"
        )

        # Esegui task
        crew = Crew(
            agents=[intelligence_agent.agent],
            tasks=[task],
            process=Process.sequential,
            verbose=True
        )

        try:
            result = crew.kickoff()

            # Aggiungi conoscenza
            knowledge = {
                "type": "intelligence_report",
                "phase": "intelligence_gathering",
                "iteration": self.iteration_count,
                "content": str(result),
                "target": self.objective_status.target
            }
            intelligence_agent.add_knowledge(knowledge)
            self.broadcast_knowledge('intelligence', knowledge)

            # Aggiorna findings
            self.objective_status.findings.append({
                "type": "intelligence",
                "content": str(result),
                "timestamp": time.time(),
                "iteration": self.iteration_count
            })

            self.logger.info("Intelligence gathering completato")

        except Exception as e:
            self.logger.error(f"Errore in intelligence phase: {e}")
            self.send_message("intelligence", "all", "alert", f"Errore: {str(e)}")

    async def _web_search_phase(self):
        """Fase di ricerca web e OSINT"""
        self.logger.info("Fase Web Search & OSINT")

        web_agent = self.agents['web_search']

        # Recupera messaggi e contesto
        messages = web_agent.get_pending_messages()
        context = self._build_context_for_agent('web_search')

        # Estrai richieste di ricerca dai messaggi
        search_requests = self._extract_search_requests(messages)

        task_description = f"""
        WEB SEARCH & OSINT PHASE - Iterazione {self.iteration_count}

        Target: {self.objective_status.target}
        Obiettivo: {self.objective_status.objective}

        Richieste di ricerca ricevute: {len(search_requests)}
        {self._format_search_requests(search_requests)}

        Contesto attuale:
        {context}

        COMPITI:
        1. Esegui ricerche web con dorks personalizzate per il target
        2. Cerca vulnerabilità note per servizi identificati
        3. Trova exploit e PoC disponibili
        4. Testa link specifici se richiesto
        5. Raccogli informazioni OSINT sul target

        Dorks suggerite per {self.objective_status.target}:
        - site:{self.objective_status.target} filetype:pdf
        - site:{self.objective_status.target} inurl:admin
        - site:{self.objective_status.target} inurl:login
        - "{self.objective_status.target}" vulnerability
        - "{self.objective_status.target}" exploit

        Rispondi con:
        - Risultati delle ricerche
        - Link interessanti trovati
        - Vulnerabilità identificate
        - Raccomandazioni per test specifici
        """

        task = Task(
            description=task_description,
            agent=web_agent.agent,
            expected_output="Report di web intelligence con vulnerabilità e exploit trovati"
        )

        # Esegui task
        crew = Crew(
            agents=[web_agent.agent],
            tasks=[task],
            process=Process.sequential,
            verbose=True
        )

        try:
            result = crew.kickoff()

            # Aggiungi conoscenza
            knowledge = {
                "type": "web_intelligence",
                "phase": "web_search",
                "iteration": self.iteration_count,
                "content": str(result),
                "target": self.objective_status.target
            }
            web_agent.add_knowledge(knowledge)
            self.broadcast_knowledge('web_search', knowledge)

            # Aggiorna findings
            self.objective_status.findings.append({
                "type": "web_intelligence",
                "content": str(result),
                "timestamp": time.time(),
                "iteration": self.iteration_count
            })

            self.logger.info("Web search completato")

        except Exception as e:
            self.logger.error(f"Errore in web search phase: {e}")
            self.send_message("web_search", "all", "alert", f"Errore: {str(e)}")

    async def _execution_phase(self):
        """Fase di esecuzione comandi"""
        self.logger.info("Fase Command Execution")

        terminal_agent = self.agents['terminal']

        # Recupera messaggi e contesto
        messages = terminal_agent.get_pending_messages()
        context = self._build_context_for_agent('terminal')

        # Estrai comandi richiesti dai messaggi
        command_requests = self._extract_command_requests(messages)

        task_description = f"""
        COMMAND EXECUTION PHASE - Iterazione {self.iteration_count}

        Target: {self.objective_status.target}
        Obiettivo: {self.objective_status.objective}

        Comandi richiesti: {len(command_requests)}
        {self._format_command_requests(command_requests)}

        Contesto attuale:
        {context}

        COMPITI:
        1. Esegui i comandi richiesti dagli altri agenti
        2. Se non ci sono comandi specifici, esegui reconnaissance di base
        3. Condividi l'output completo con gli altri agenti
        4. Segnala eventuali errori o problemi

        Comandi di base per reconnaissance:
        - nmap -sS -sV -O {self.objective_status.target}
        - nmap -sC -sV {self.objective_status.target}
        - whatweb {self.objective_status.target}
        - dig {self.objective_status.target}

        IMPORTANTE: Esegui solo comandi sicuri e autorizzati.
        Condividi TUTTO l'output con gli altri agenti per analisi.
        """

        task = Task(
            description=task_description,
            agent=terminal_agent.agent,
            expected_output="Output completo dei comandi eseguiti con analisi preliminare"
        )

        # Esegui task
        crew = Crew(
            agents=[terminal_agent.agent],
            tasks=[task],
            process=Process.sequential,
            verbose=True
        )

        try:
            result = crew.kickoff()

            # Aggiungi conoscenza
            knowledge = {
                "type": "command_execution",
                "phase": "execution",
                "iteration": self.iteration_count,
                "content": str(result),
                "target": self.objective_status.target
            }
            terminal_agent.add_knowledge(knowledge)
            self.broadcast_knowledge('terminal', knowledge)

            # Invia output agli altri agenti per analisi
            self.send_message(
                "terminal", "all", "result",
                f"Output comandi iterazione {self.iteration_count}: {str(result)}"
            )

            # Aggiorna findings
            self.objective_status.findings.append({
                "type": "command_execution",
                "content": str(result),
                "timestamp": time.time(),
                "iteration": self.iteration_count
            })

            self.logger.info("Command execution completato")

        except Exception as e:
            self.logger.error(f"Errore in execution phase: {e}")
            self.send_message("terminal", "all", "alert", f"Errore esecuzione: {str(e)}")

    async def _analysis_phase(self):
        """Fase di analisi e pianificazione"""
        self.logger.info("Fase Analysis & Planning")

        analysis_agent = self.agents['analysis']

        # Recupera messaggi e contesto
        messages = analysis_agent.get_pending_messages()
        context = self._build_context_for_agent('analysis')

        # Recupera tutti i findings dell'iterazione corrente
        current_findings = [f for f in self.objective_status.findings
                          if f.get('iteration') == self.iteration_count]

        task_description = f"""
        ANALYSIS & PLANNING PHASE - Iterazione {self.iteration_count}

        Target: {self.objective_status.target}
        Obiettivo: {self.objective_status.objective}
        Progresso attuale: {self.objective_status.progress:.1%}

        Findings di questa iterazione: {len(current_findings)}
        {self._format_findings(current_findings)}

        Messaggi ricevuti: {len(messages)}
        {self._format_messages(messages)}

        Contesto completo:
        {context}

        COMPITI:
        1. Analizza tutti i risultati ottenuti in questa iterazione
        2. Identifica vulnerabilità e opportunità di exploit
        3. Valuta il progresso verso l'obiettivo
        4. Pianifica le prossime azioni specifiche
        5. Determina se l'obiettivo è stato raggiunto

        CRITERI DI COMPLETAMENTO:
        {self._format_completion_criteria()}

        Rispondi con:
        - Analisi dei risultati
        - Vulnerabilità identificate
        - Progresso stimato (0-100%)
        - Prossime azioni specifiche
        - Comandi da eseguire nella prossima iterazione
        - Ricerche web da effettuare
        - Stato obiettivo (attivo/completato/fallito)
        """

        task = Task(
            description=task_description,
            agent=analysis_agent.agent,
            expected_output="Analisi completa con piano per la prossima iterazione"
        )

        # Esegui task
        crew = Crew(
            agents=[analysis_agent.agent],
            tasks=[task],
            process=Process.sequential,
            verbose=True
        )

        try:
            result = crew.kickoff()
            result_str = str(result)

            # Aggiungi conoscenza
            knowledge = {
                "type": "analysis_report",
                "phase": "analysis",
                "iteration": self.iteration_count,
                "content": result_str,
                "target": self.objective_status.target
            }
            analysis_agent.add_knowledge(knowledge)
            self.broadcast_knowledge('analysis', knowledge)

            # Estrai informazioni dall'analisi
            progress = self._extract_progress(result_str)
            next_actions = self._extract_next_actions(result_str)

            # Aggiorna stato obiettivo
            self.objective_status.progress = progress
            self.objective_status.next_actions = next_actions
            self.objective_status.timestamp = time.time()

            # Aggiorna findings
            self.objective_status.findings.append({
                "type": "analysis",
                "content": result_str,
                "timestamp": time.time(),
                "iteration": self.iteration_count,
                "progress": progress
            })

            # Invia piano agli altri agenti
            self.send_message(
                "analysis", "all", "info",
                f"Piano iterazione {self.iteration_count + 1}: {'; '.join(next_actions)}"
            )

            self.logger.info(f"Analysis completata - Progresso: {progress:.1%}")

        except Exception as e:
            self.logger.error(f"Errore in analysis phase: {e}")
            self.send_message("analysis", "all", "alert", f"Errore analisi: {str(e)}")

    async def _check_objective_completion(self) -> bool:
        """Verifica se l'obiettivo è stato completato"""

        # Verifica criteri di completamento
        completed_criteria = 0
        total_criteria = len(self.objective_status.completion_criteria)

        for finding in self.objective_status.findings:
            content = finding.get('content', '').lower()

            # Cerca indicatori di successo
            if any(keyword in content for keyword in [
                'vulnerabilità', 'exploit', 'accesso', 'compromesso',
                'shell', 'root', 'admin', 'password'
            ]):
                completed_criteria += 1
                break

        # Verifica progresso
        if self.objective_status.progress >= 0.8:  # 80% di progresso
            completed_criteria += 1

        # Obiettivo completato se almeno metà dei criteri sono soddisfatti
        completion_ratio = completed_criteria / max(total_criteria, 1)

        if completion_ratio >= 0.5:
            self.logger.info(f"Obiettivo completato! Criteri soddisfatti: {completion_ratio:.1%}")
            return True

        return False

    async def _finalize_results(self) -> Dict[str, Any]:
        """Finalizza i risultati del penetration test"""

        end_time = time.time()
        duration = end_time - self.objective_status.timestamp

        # Compila risultati finali
        results = {
            "target": self.objective_status.target,
            "objective": self.objective_status.objective,
            "status": self.objective_status.status,
            "progress": self.objective_status.progress,
            "iterations": self.iteration_count,
            "duration_seconds": duration,
            "findings": self.objective_status.findings,
            "total_messages": len(self.message_bus),
            "agents_involved": list(self.agents.keys()),
            "completion_time": datetime.fromtimestamp(end_time).isoformat()
        }

        # Log finale
        self.logger.info(f"Penetration test completato: {self.objective_status.status}")
        self.logger.info(f"Progresso finale: {self.objective_status.progress:.1%}")
        self.logger.info(f"Iterazioni: {self.iteration_count}")
        self.logger.info(f"Durata: {duration:.1f} secondi")

        return results

    def _build_context_for_agent(self, agent_name: str) -> str:
        """Costruisce contesto per un agente specifico"""
        agent = self.agents[agent_name]

        # Recupera conoscenza rilevante
        relevant_knowledge = agent.get_relevant_knowledge(self.objective_status.target)

        # Costruisci contesto
        context_parts = [
            f"Target: {self.objective_status.target}",
            f"Obiettivo: {self.objective_status.objective}",
            f"Progresso: {self.objective_status.progress:.1%}",
            f"Iterazione: {self.iteration_count}/{self.max_iterations}",
            "",
            "Conoscenza rilevante:",
        ]

        for knowledge in relevant_knowledge[-5:]:  # Ultimi 5 elementi
            context_parts.append(f"- {knowledge.get('type', 'unknown')}: {str(knowledge.get('content', ''))[:200]}...")

        return "\n".join(context_parts)

    def _format_messages(self, messages: List[AgentMessage]) -> str:
        """Formatta messaggi per visualizzazione"""
        if not messages:
            return "Nessun messaggio"

        formatted = []
        for msg in messages[-5:]:  # Ultimi 5 messaggi
            formatted.append(f"- {msg.from_agent} -> {msg.message_type}: {msg.content[:100]}...")

        return "\n".join(formatted)

    def _format_findings(self, findings: List[Dict[str, Any]]) -> str:
        """Formatta findings per visualizzazione"""
        if not findings:
            return "Nessun finding"

        formatted = []
        for finding in findings:
            formatted.append(f"- {finding.get('type', 'unknown')}: {str(finding.get('content', ''))[:150]}...")

        return "\n".join(formatted)

    def _format_completion_criteria(self) -> str:
        """Formatta criteri di completamento"""
        return "\n".join([f"- {criteria}" for criteria in self.objective_status.completion_criteria])

    def _extract_search_requests(self, messages: List[AgentMessage]) -> List[str]:
        """Estrae richieste di ricerca dai messaggi"""
        requests = []
        for msg in messages:
            if 'search' in msg.content.lower() or 'cerca' in msg.content.lower():
                requests.append(msg.content)
        return requests

    def _extract_command_requests(self, messages: List[AgentMessage]) -> List[str]:
        """Estrae richieste di comandi dai messaggi"""
        requests = []
        for msg in messages:
            if any(keyword in msg.content.lower() for keyword in ['nmap', 'scan', 'comando', 'esegui']):
                requests.append(msg.content)
        return requests

    def _format_search_requests(self, requests: List[str]) -> str:
        """Formatta richieste di ricerca"""
        if not requests:
            return "Nessuna richiesta specifica"
        return "\n".join([f"- {req[:100]}..." for req in requests])

    def _format_command_requests(self, requests: List[str]) -> str:
        """Formatta richieste di comandi"""
        if not requests:
            return "Nessuna richiesta specifica"
        return "\n".join([f"- {req[:100]}..." for req in requests])

    def _extract_progress(self, analysis_text: str) -> float:
        """Estrae progresso dall'analisi"""
        import re

        # Cerca pattern di percentuale
        progress_patterns = [
            r'progresso[:\s]*(\d+)%',
            r'completato[:\s]*(\d+)%',
            r'(\d+)%[:\s]*completato',
            r'progress[:\s]*(\d+)%'
        ]

        for pattern in progress_patterns:
            match = re.search(pattern, analysis_text.lower())
            if match:
                return float(match.group(1)) / 100.0

        # Stima basata su parole chiave
        keywords = ['vulnerabilità', 'exploit', 'accesso', 'successo']
        found_keywords = sum(1 for keyword in keywords if keyword in analysis_text.lower())

        return min(self.objective_status.progress + (found_keywords * 0.2), 1.0)

    def _extract_next_actions(self, analysis_text: str) -> List[str]:
        """Estrae prossime azioni dall'analisi"""
        actions = []

        # Cerca sezioni di azioni
        lines = analysis_text.split('\n')
        in_actions_section = False

        for line in lines:
            line = line.strip()

            if any(keyword in line.lower() for keyword in ['prossim', 'next', 'azioni', 'actions']):
                in_actions_section = True
                continue

            if in_actions_section and line:
                if line.startswith('-') or line.startswith('*') or line.startswith('•'):
                    actions.append(line[1:].strip())
                elif line[0].isdigit():
                    actions.append(line.split('.', 1)[-1].strip())

        # Se non trovate azioni specifiche, usa azioni di default
        if not actions:
            actions = [
                f"Continua reconnaissance su {self.objective_status.target}",
                "Cerca vulnerabilità specifiche",
                "Testa exploit disponibili"
            ]

        return actions[:5]  # Massimo 5 azioni


# Istanza globale
collaborative_crew = CollaborativeCrew()
