"""
Configuration Manager per il sistema multi-agent
Gestisce la configurazione da file YAML e variabili d'ambiente
"""

import os
import yaml
import logging
from typing import Dict, Any, Optional, List
from dataclasses import dataclass
from pathlib import Path


@dataclass
class AgentConfig:
    """Configurazione per un singolo agente"""
    role: str
    goal: str
    max_iterations: int = 3
    allow_delegation: bool = True
    verbose: bool = True
    model: str = "qwen3_brain"
    agent_type: str = "hacker"


@dataclass
class ToolConfig:
    """Configurazione per un tool"""
    enabled: bool = True
    timeout: int = 300
    options: Dict[str, Any] = None


@dataclass
class SecurityConfig:
    """Configurazione di sicurezza"""
    safe_mode: bool = True
    require_authorization: bool = True
    log_all_activities: bool = True
    allowed_targets: List[str] = None
    forbidden_actions: List[str] = None


class ConfigManager:
    """
    Manager per la configurazione del sistema multi-agent
    """
    
    def __init__(self, config_path: str = "config/pentest_config.yaml"):
        self.config_path = config_path
        self.config: Dict[str, Any] = {}
        self.logger = logging.getLogger("heka.config_manager")
        
        self.load_config()
    
    def load_config(self):
        """Carica la configurazione da file YAML"""
        try:
            config_file = Path(self.config_path)
            
            if config_file.exists():
                with open(config_file, 'r', encoding='utf-8') as f:
                    self.config = yaml.safe_load(f) or {}
                self.logger.info(f"Configurazione caricata da {self.config_path}")
            else:
                self.logger.warning(f"File configurazione non trovato: {self.config_path}")
                self.config = self._get_default_config()
                
            # Override con variabili d'ambiente
            self._apply_env_overrides()
            
        except Exception as e:
            self.logger.error(f"Errore caricamento configurazione: {e}")
            self.config = self._get_default_config()
    
    def _apply_env_overrides(self):
        """Applica override da variabili d'ambiente"""
        # AI Client URLs
        if 'ai_client' not in self.config:
            self.config['ai_client'] = {}
        if 'models' not in self.config['ai_client']:
            self.config['ai_client']['models'] = {}
            
        # Override URL modelli AI
        for model_name in ['qwen3_brain', 'qwen3_coder']:
            env_var = f"{model_name.upper()}_API_URL"
            if env_var in os.environ:
                if model_name not in self.config['ai_client']['models']:
                    self.config['ai_client']['models'][model_name] = {}
                self.config['ai_client']['models'][model_name]['url'] = os.environ[env_var]
        
        # Override log level
        if 'LOG_LEVEL' in os.environ:
            if 'logging' not in self.config:
                self.config['logging'] = {}
            self.config['logging']['level'] = os.environ['LOG_LEVEL']
    
    def _get_default_config(self) -> Dict[str, Any]:
        """Restituisce configurazione di default"""
        return {
            'agents': {
                'reconnaissance': {
                    'role': 'Reconnaissance Specialist',
                    'goal': 'Raccogliere informazioni dettagliate sui target',
                    'max_iterations': 3,
                    'allow_delegation': True,
                    'verbose': True,
                    'model': 'qwen3_brain',
                    'agent_type': 'hacker'
                },
                'exploitation': {
                    'role': 'Exploitation Expert',
                    'goal': 'Identificare e sfruttare vulnerabilità',
                    'max_iterations': 3,
                    'allow_delegation': True,
                    'verbose': True,
                    'model': 'qwen3_coder',
                    'agent_type': 'hacker'
                },
                'linux_admin': {
                    'role': 'Linux Systems Expert',
                    'goal': 'Analizzare e proteggere sistemi Linux',
                    'max_iterations': 3,
                    'allow_delegation': True,
                    'verbose': True,
                    'model': 'qwen3_brain',
                    'agent_type': 'hacker'
                },
                'reporting': {
                    'role': 'Security Analyst & Reporter',
                    'goal': 'Creare report dettagliati',
                    'max_iterations': 2,
                    'allow_delegation': False,
                    'verbose': True,
                    'model': 'qwen3_brain',
                    'agent_type': 'researcher'
                },
                'web_intelligence': {
                    'role': 'Web Intelligence Gatherer',
                    'goal': 'Raccogliere intelligence web per supportare altri agenti',
                    'max_iterations': 3,
                    'allow_delegation': True,
                    'verbose': True,
                    'model': 'qwen3_brain',
                    'agent_type': 'researcher'
                }
            },
            'tools': {
                'network_scan': {'enabled': True, 'timeout': 300},
                'vulnerability_scan': {'enabled': True, 'timeout': 600},
                'exploit': {'enabled': True, 'timeout': 180},
                'linux_command': {'enabled': True, 'timeout': 60},
                'web_browser': {'enabled': True, 'timeout': 120, 'max_pages_per_session': 10}
            },
            'security': {
                'safe_mode': True,
                'require_authorization': True,
                'log_all_activities': True,
                'allowed_targets': ['192.168.1.0/24', '10.0.0.0/8'],
                'forbidden_actions': ['data_destruction', 'service_disruption']
            },
            'logging': {
                'level': 'INFO',
                'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
            }
        }
    
    def get_agent_config(self, agent_name: str) -> AgentConfig:
        """Restituisce configurazione per un agente specifico"""
        agent_data = self.config.get('agents', {}).get(agent_name, {})
        
        return AgentConfig(
            role=agent_data.get('role', f'{agent_name.title()} Agent'),
            goal=agent_data.get('goal', f'Esegui task di {agent_name}'),
            max_iterations=agent_data.get('max_iterations', 3),
            allow_delegation=agent_data.get('allow_delegation', True),
            verbose=agent_data.get('verbose', True),
            model=agent_data.get('model', 'qwen3_brain'),
            agent_type=agent_data.get('agent_type', 'hacker')
        )
    
    def get_tool_config(self, tool_name: str) -> ToolConfig:
        """Restituisce configurazione per un tool specifico"""
        tool_data = self.config.get('tools', {}).get(tool_name, {})
        
        return ToolConfig(
            enabled=tool_data.get('enabled', True),
            timeout=tool_data.get('timeout', 300),
            options=tool_data.get('options', {})
        )
    
    def get_security_config(self) -> SecurityConfig:
        """Restituisce configurazione di sicurezza"""
        security_data = self.config.get('security', {})
        
        return SecurityConfig(
            safe_mode=security_data.get('safe_mode', True),
            require_authorization=security_data.get('require_authorization', True),
            log_all_activities=security_data.get('log_all_activities', True),
            allowed_targets=security_data.get('allowed_targets', []),
            forbidden_actions=security_data.get('forbidden_actions', [])
        )
    
    def get_ai_model_config(self, model_name: str) -> Dict[str, Any]:
        """Restituisce configurazione per un modello AI"""
        models_config = self.config.get('ai_client', {}).get('models', {})
        return models_config.get(model_name, {})
    
    def get_logging_config(self) -> Dict[str, Any]:
        """Restituisce configurazione logging"""
        return self.config.get('logging', {
            'level': 'INFO',
            'format': '%(asctime)s - %(name)s - %(levelname)s - %(message)s'
        })
    
    def get_crew_config(self) -> Dict[str, Any]:
        """Restituisce configurazione crew"""
        return self.config.get('crew', {
            'process': 'sequential',
            'verbose': True,
            'memory': True,
            'cache': True,
            'max_rpm': 10
        })
    
    def get_task_config(self, task_name: str) -> Dict[str, Any]:
        """Restituisce configurazione per un task specifico"""
        tasks_config = self.config.get('tasks', {})
        return tasks_config.get(task_name, {
            'timeout': 1800,
            'required_outputs': []
        })
    
    def is_target_allowed(self, target: str) -> bool:
        """Verifica se un target è autorizzato"""
        security_config = self.get_security_config()
        
        if not security_config.require_authorization:
            return True
            
        if not security_config.allowed_targets:
            return True
            
        # Verifica se il target è nella lista degli autorizzati
        # Implementazione semplificata - in produzione usare ipaddress module
        for allowed in security_config.allowed_targets:
            if target in allowed or allowed in target:
                return True
                
        return False
    
    def is_action_forbidden(self, action: str) -> bool:
        """Verifica se un'azione è vietata"""
        security_config = self.get_security_config()
        return action in (security_config.forbidden_actions or [])
    
    def save_config(self, config_path: Optional[str] = None):
        """Salva la configurazione corrente su file"""
        save_path = config_path or self.config_path
        
        try:
            # Crea directory se non esiste
            Path(save_path).parent.mkdir(parents=True, exist_ok=True)
            
            with open(save_path, 'w', encoding='utf-8') as f:
                yaml.dump(self.config, f, default_flow_style=False, indent=2)
                
            self.logger.info(f"Configurazione salvata in {save_path}")
            
        except Exception as e:
            self.logger.error(f"Errore salvataggio configurazione: {e}")
    
    def update_config(self, section: str, key: str, value: Any):
        """Aggiorna un valore nella configurazione"""
        if section not in self.config:
            self.config[section] = {}
            
        self.config[section][key] = value
        self.logger.info(f"Configurazione aggiornata: {section}.{key} = {value}")
    
    def get_config_value(self, section: str, key: str, default: Any = None) -> Any:
        """Ottiene un valore dalla configurazione"""
        return self.config.get(section, {}).get(key, default)


# Istanza globale
config_manager = ConfigManager()
