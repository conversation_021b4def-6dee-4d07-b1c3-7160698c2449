"""
Sistema Multi-Agent CrewAI per Penetration Testing e Sistemi Linux
Utilizza ai_client.py come LLM backend e i prompt dalla cartella prompts
"""

import os
import asyncio
from typing import Dict, List, Any, Optional
from crewai import Agent, Task, Crew, Process
from crewai.tools import BaseTool
from pydantic import BaseModel, Field

from .crew_ai_llm import ReconnaissanceLLM, ExploitationLLM, LinuxAdminLLM, ReportingLLM, WebIntelligenceLLM, KaliExecutorLLM
from .ai_client import prompt_manager
from .config_manager import config_manager
from .web_browser_tool import WebBrowserTool
from .kali_executor_tool import KaliExecutorTool


class NetworkScanTool(BaseTool):
    """Tool per scanning di rete"""
    name: str = "network_scan"
    description: str = "Esegue scansioni di rete con nmap e altri strumenti"
    
    def _run(self, target: str, scan_type: str = "basic") -> str:
        """Esegue una scansione di rete"""
        # Simulazione - in produzione eseguirebbe nmap reale
        return f"Scansione {scan_type} completata su {target}:\n" \
               f"- Porte aperte: 22, 80, 443\n" \
               f"- Servizi: SSH, HTTP, HTTPS\n" \
               f"- OS Detection: Linux Ubuntu 20.04"


class VulnerabilityScanTool(BaseTool):
    """Tool per scansione vulnerabilità"""
    name: str = "vulnerability_scan"
    description: str = "Scansiona vulnerabilità con Nessus, OpenVAS, etc."
    
    def _run(self, target: str, scan_depth: str = "standard") -> str:
        """Esegue scansione vulnerabilità"""
        return f"Scansione vulnerabilità {scan_depth} su {target}:\n" \
               f"- CVE-2023-1234: SQL Injection (High)\n" \
               f"- CVE-2023-5678: XSS (Medium)\n" \
               f"- Configurazioni deboli SSH (Low)"


class ExploitTool(BaseTool):
    """Tool per exploitation"""
    name: str = "exploit"
    description: str = "Esegue exploit controllati con Metasploit"
    
    def _run(self, target: str, exploit_type: str, payload: str = "") -> str:
        """Esegue un exploit"""
        return f"Exploit {exploit_type} eseguito su {target}:\n" \
               f"- Payload: {payload}\n" \
               f"- Status: Shell ottenuta\n" \
               f"- Privilegi: user-level"


class LinuxCommandTool(BaseTool):
    """Tool per comandi Linux"""
    name: str = "linux_command"
    description: str = "Esegue comandi Linux per amministrazione e analisi"
    
    def _run(self, command: str, target: str = "localhost") -> str:
        """Esegue comando Linux"""
        # Simulazione - in produzione eseguirebbe comandi reali
        if "ps aux" in command:
            return "Processi in esecuzione:\nroot 1 init\nwww-data 1234 apache2"
        elif "netstat" in command:
            return "Connessioni di rete:\ntcp 0.0.0.0:22 LISTEN\ntcp 0.0.0.0:80 LISTEN"
        else:
            return f"Comando '{command}' eseguito su {target}"


class PentestCrew:
    """
    Crew di agenti specializzati per penetration testing
    """
    
    def __init__(self):
        self.tools = self._setup_tools()
        self.agents = self._setup_agents()
        self.crew = None
    
    def _setup_tools(self) -> Dict[str, BaseTool]:
        """Configura gli strumenti disponibili"""
        return {
            'network_scan': NetworkScanTool(),
            'vulnerability_scan': VulnerabilityScanTool(),
            'exploit': ExploitTool(),
            'linux_command': LinuxCommandTool(),
            'web_browser': WebBrowserTool(),
            'kali_executor': KaliExecutorTool()
        }
    
    def _setup_agents(self) -> Dict[str, Agent]:
        """Configura gli agenti specializzati"""

        # Carica configurazioni
        recon_config = config_manager.get_agent_config('reconnaissance')
        exploit_config = config_manager.get_agent_config('exploitation')
        linux_config = config_manager.get_agent_config('linux_admin')
        report_config = config_manager.get_agent_config('reporting')
        web_intel_config = config_manager.get_agent_config('web_intelligence')

        # Agente Reconnaissance
        reconnaissance_agent = Agent(
            role=recon_config.role,
            goal=recon_config.goal,
            backstory=prompt_manager.load_prompt("hacker", "agent.system.main.role.md") +
                     "\n" + prompt_manager.load_prompt("hacker", "reconnaissance.md"),
            llm=ReconnaissanceLLM(),
            tools=[self.tools['network_scan'], self.tools['vulnerability_scan']],
            verbose=recon_config.verbose,
            allow_delegation=recon_config.allow_delegation,
            max_iter=recon_config.max_iterations
        )
        
        # Agente Exploitation
        exploitation_agent = Agent(
            role=exploit_config.role,
            goal=exploit_config.goal,
            backstory=prompt_manager.load_prompt("hacker", "agent.system.main.role.md") +
                     "\n" + prompt_manager.load_prompt("hacker", "exploitation.md"),
            llm=ExploitationLLM(),
            tools=[self.tools['exploit'], self.tools['linux_command']],
            verbose=exploit_config.verbose,
            allow_delegation=exploit_config.allow_delegation,
            max_iter=exploit_config.max_iterations
        )
        
        # Agente Linux Administration
        linux_admin_agent = Agent(
            role=linux_config.role,
            goal=linux_config.goal,
            backstory=prompt_manager.load_prompt("hacker", "agent.system.main.role.md") +
                     "\n" + prompt_manager.load_prompt("hacker", "linux_admin.md"),
            llm=LinuxAdminLLM(),
            tools=[self.tools['linux_command']],
            verbose=linux_config.verbose,
            allow_delegation=linux_config.allow_delegation,
            max_iter=linux_config.max_iterations
        )

        # Agente Reporting
        reporting_agent = Agent(
            role=report_config.role,
            goal=report_config.goal,
            backstory=prompt_manager.load_prompt("researcher", "agent.system.main.role.md") +
                     "\nSpecializzato in analisi e documentazione di sicurezza.",
            llm=ReportingLLM(),
            tools=[],
            verbose=report_config.verbose,
            allow_delegation=report_config.allow_delegation,
            max_iter=report_config.max_iterations
        )

        # Agente Web Intelligence
        web_intelligence_agent = Agent(
            role=web_intel_config.role,
            goal=web_intel_config.goal,
            backstory=prompt_manager.load_prompt("researcher", "agent.system.main.role.md") +
                     "\n" + prompt_manager.load_prompt("researcher", "web_intelligence.md"),
            llm=WebIntelligenceLLM(),
            tools=[self.tools['web_browser']],
            verbose=web_intel_config.verbose,
            allow_delegation=web_intel_config.allow_delegation,
            max_iter=web_intel_config.max_iterations
        )

        # Agente Kali Executor
        kali_executor_config = config_manager.get_agent_config('kali_executor')
        kali_executor_agent = Agent(
            role=kali_executor_config.role,
            goal=kali_executor_config.goal,
            backstory=prompt_manager.load_prompt("executor", "agent.system.main.role.md") +
                     "\n" + prompt_manager.load_prompt("executor", "kali_executor.md"),
            llm=KaliExecutorLLM(),
            tools=[self.tools['kali_executor']],
            verbose=kali_executor_config.verbose,
            allow_delegation=kali_executor_config.allow_delegation,
            max_iter=kali_executor_config.max_iterations
        )

        return {
            'reconnaissance': reconnaissance_agent,
            'exploitation': exploitation_agent,
            'linux_admin': linux_admin_agent,
            'reporting': reporting_agent,
            'web_intelligence': web_intelligence_agent,
            'kali_executor': kali_executor_agent
        }
    
    def create_pentest_tasks(self, target: str, scope: str = "full") -> List[Task]:
        """Crea task per un penetration test completo"""
        
        tasks = []
        
        # Task 1: Reconnaissance
        recon_task = Task(
            description=f"""
            Esegui reconnaissance completo sul target: {target}
            
            Attività richieste:
            1. Scansione di rete per identificare servizi attivi
            2. Scansione vulnerabilità per identificare possibili exploit
            3. Raccolta informazioni OSINT
            4. Analisi superficie di attacco
            
            Fornisci un report dettagliato con:
            - Servizi identificati
            - Vulnerabilità trovate
            - Raccomandazioni per exploitation
            """,
            agent=self.agents['reconnaissance'],
            expected_output="Report dettagliato di reconnaissance con servizi, vulnerabilità e raccomandazioni"
        )
        tasks.append(recon_task)

        # Task 2: Web Intelligence Gathering
        web_intel_task = Task(
            description=f"""
            Supporta il team di penetration testing raccogliendo intelligence web su: {target}

            Attività richieste:
            1. Ricerca OSINT approfondita sul target
            2. Cerca informazioni su tecnologie utilizzate
            3. Identifica possibili vulnerabilità note online
            4. Raccogli informazioni su configurazioni e servizi
            5. Cerca exploit pubblici per le tecnologie identificate

            Fornisci intelligence utile per:
            - Supportare l'exploitation con informazioni specifiche
            - Identificare vettori di attacco aggiuntivi
            - Fornire contesto per l'analisi delle vulnerabilità

            Utilizza il web browser tool per ricerche mirate e analisi.
            """,
            agent=self.agents['web_intelligence'],
            expected_output="Report di intelligence web con informazioni utili per exploitation e analisi"
        )
        tasks.append(web_intel_task)

        # Task 3: Exploitation
        exploit_task = Task(
            description=f"""
            Basandoti sui risultati del reconnaissance, esegui exploitation controllata:
            
            Attività richieste:
            1. Prioritizza vulnerabilità da sfruttare
            2. Esegui exploit per ottenere accesso iniziale
            3. Esegui privilege escalation se possibile
            4. Documenta tecniche utilizzate
            
            Fornisci:
            - Exploit utilizzati con successo
            - Livello di accesso ottenuto
            - Evidenze di compromissione
            """,
            agent=self.agents['exploitation'],
            expected_output="Report di exploitation con tecniche utilizzate e accesso ottenuto",
            context=[recon_task]
        )
        tasks.append(exploit_task)
        
        # Task 3: Linux System Analysis
        linux_task = Task(
            description=f"""
            Analizza il sistema Linux compromesso e implementa hardening:
            
            Attività richieste:
            1. Analisi configurazione sistema
            2. Identificazione misure di sicurezza mancanti
            3. Raccomandazioni di hardening
            4. Verifica log e tracce di compromissione
            
            Fornisci:
            - Stato attuale della sicurezza
            - Raccomandazioni di hardening
            - Script di miglioramento sicurezza
            """,
            agent=self.agents['linux_admin'],
            expected_output="Analisi sistema Linux con raccomandazioni di hardening",
            context=[exploit_task]
        )
        tasks.append(linux_task)
        
        # Task 4: Final Report
        report_task = Task(
            description=f"""
            Crea un report esecutivo completo del penetration test:
            
            Consolida tutti i risultati in un report che includa:
            1. Executive Summary
            2. Metodologia utilizzata
            3. Vulnerabilità identificate (con CVSS)
            4. Exploit riusciti
            5. Raccomandazioni di remediation
            6. Timeline delle attività
            
            Il report deve essere professionale e adatto per management e team tecnico.
            """,
            agent=self.agents['reporting'],
            expected_output="Report esecutivo completo di penetration testing",
            context=[recon_task, exploit_task, linux_task]
        )
        tasks.append(report_task)
        
        return tasks
    
    def create_linux_admin_tasks(self, system_info: str) -> List[Task]:
        """Crea task per amministrazione Linux"""
        
        tasks = []
        
        # Task: System Hardening
        hardening_task = Task(
            description=f"""
            Analizza e migliora la sicurezza del sistema Linux:
            
            Sistema: {system_info}
            
            Attività:
            1. Audit configurazione sicurezza
            2. Verifica patch e aggiornamenti
            3. Analisi servizi e porte aperte
            4. Configurazione firewall
            5. Hardening SSH e servizi critici
            
            Fornisci script e comandi specifici per implementare le migliorie.
            """,
            agent=self.agents['linux_admin'],
            expected_output="Piano di hardening con script e comandi specifici"
        )
        tasks.append(hardening_task)
        
        return tasks
    
    def execute_pentest(self, target: str, scope: str = "full") -> str:
        """Esegue un penetration test completo"""
        
        tasks = self.create_pentest_tasks(target, scope)
        
        self.crew = Crew(
            agents=list(self.agents.values()),
            tasks=tasks,
            process=Process.sequential,
            verbose=True
        )
        
        try:
            result = self.crew.kickoff()
            return str(result)
        except Exception as e:
            return f"Errore durante l'esecuzione del pentest: {str(e)}"
    
    def execute_linux_admin(self, system_info: str) -> str:
        """Esegue task di amministrazione Linux"""
        
        tasks = self.create_linux_admin_tasks(system_info)
        
        self.crew = Crew(
            agents=[self.agents['linux_admin'], self.agents['reporting']],
            tasks=tasks,
            process=Process.sequential,
            verbose=True
        )
        
        try:
            result = self.crew.kickoff()
            return str(result)
        except Exception as e:
            return f"Errore durante l'amministrazione Linux: {str(e)}"

    def execute_web_intelligence(self, research_query: str) -> str:
        """Esegue task di web intelligence"""

        # Task di ricerca web intelligence
        web_intel_task = Task(
            description=f"""
            Esegui ricerca web intelligence per: {research_query}

            Attività richieste:
            1. Ricerca informazioni specifiche sul web
            2. Analizza fonti multiple e affidabili
            3. Estrai informazioni rilevanti e utili
            4. Organizza i risultati in formato utilizzabile

            Utilizza il web browser tool per:
            - Ricerche su motori di ricerca
            - Navigazione di siti specifici
            - Analisi di contenuti web
            - Estrazione di dati strutturati

            Fornisci un report completo con:
            - Informazioni trovate
            - Fonti consultate
            - Raccomandazioni per azioni successive
            """,
            agent=self.agents['web_intelligence'],
            expected_output="Report dettagliato di web intelligence con informazioni utili e fonti"
        )

        self.crew = Crew(
            agents=[self.agents['web_intelligence']],
            tasks=[web_intel_task],
            process=Process.sequential,
            verbose=True
        )

        try:
            result = self.crew.kickoff()
            return str(result)
        except Exception as e:
            return f"Errore durante la web intelligence: {str(e)}"

    def execute_reconnaissance(self, target: str) -> str:
        """Esegue task di reconnaissance"""

        # Task di reconnaissance
        recon_task = Task(
            description=f"""
            Esegui reconnaissance completo su target: {target}

            Attività richieste:
            1. Scansione porte e servizi
            2. Enumerazione servizi attivi
            3. Identificazione tecnologie utilizzate
            4. Raccolta informazioni DNS
            5. Analisi certificati SSL/TLS

            Utilizza il Kali Executor per eseguire:
            - nmap per scansione porte
            - dig/nslookup per informazioni DNS
            - whatweb per identificazione tecnologie
            - sslscan per analisi SSL

            Fornisci un report dettagliato con:
            - Porte aperte e servizi
            - Versioni software identificate
            - Possibili vettori di attacco
            - Raccomandazioni per fasi successive
            """,
            agent=self.agents['reconnaissance'],
            expected_output="Report completo di reconnaissance con porte, servizi e vulnerabilità potenziali"
        )

        self.crew = Crew(
            agents=[self.agents['reconnaissance'], self.agents['kali_executor']],
            tasks=[recon_task],
            process=Process.sequential,
            verbose=True
        )

        try:
            result = self.crew.kickoff()
            return str(result)
        except Exception as e:
            return f"Errore durante reconnaissance: {str(e)}"

    def execute_vulnerability_assessment(self, target: str) -> str:
        """Esegue vulnerability assessment"""

        # Task di vulnerability assessment
        vuln_task = Task(
            description=f"""
            Esegui vulnerability assessment su target: {target}

            Attività richieste:
            1. Scansione vulnerabilità con scanner automatici
            2. Analisi manuale dei servizi identificati
            3. Verifica configurazioni di sicurezza
            4. Test per vulnerabilità comuni (OWASP Top 10)
            5. Analisi patch level e versioni software

            Utilizza il Kali Executor per eseguire:
            - nessus/openvas per scansione vulnerabilità
            - nikto per web application testing
            - sqlmap per SQL injection
            - dirb/gobuster per directory enumeration
            - nuclei per template-based scanning

            Fornisci un report dettagliato con:
            - Vulnerabilità identificate con severity
            - Proof of concept per vulnerabilità critiche
            - Raccomandazioni di remediation
            - Priorità di intervento
            """,
            agent=self.agents['reconnaissance'],
            expected_output="Report di vulnerability assessment con vulnerabilità classificate per severity"
        )

        self.crew = Crew(
            agents=[self.agents['reconnaissance'], self.agents['kali_executor']],
            tasks=[vuln_task],
            process=Process.sequential,
            verbose=True
        )

        try:
            result = self.crew.kickoff()
            return str(result)
        except Exception as e:
            return f"Errore durante vulnerability assessment: {str(e)}"

    def execute_exploitation(self, target: str) -> str:
        """Esegue exploitation delle vulnerabilità"""

        # Task di exploitation
        exploit_task = Task(
            description=f"""
            Esegui exploitation su target: {target}

            Attività richieste:
            1. Sfruttamento vulnerabilità identificate
            2. Escalation privilegi se possibile
            3. Mantenimento accesso
            4. Raccolta informazioni sensibili
            5. Documentazione completa degli exploit

            Utilizza il Kali Executor per eseguire:
            - metasploit per exploitation automatico
            - exploit manuali personalizzati
            - privilege escalation tools
            - post-exploitation frameworks
            - data exfiltration tools

            IMPORTANTE: Esegui solo exploitation etico e autorizzato

            Fornisci un report dettagliato con:
            - Exploit utilizzati con successo
            - Livello di accesso ottenuto
            - Dati sensibili identificati
            - Impatto business delle vulnerabilità
            - Raccomandazioni immediate
            """,
            agent=self.agents['exploitation'],
            expected_output="Report di exploitation con dettagli degli exploit riusciti e impatto"
        )

        self.crew = Crew(
            agents=[self.agents['exploitation'], self.agents['kali_executor']],
            tasks=[exploit_task],
            process=Process.sequential,
            verbose=True
        )

        try:
            result = self.crew.kickoff()
            return str(result)
        except Exception as e:
            return f"Errore durante exploitation: {str(e)}"


# Istanza globale
pentest_crew = PentestCrew()
