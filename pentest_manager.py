#!/usr/bin/env python3
"""
Heka Penetration Testing Manager
Sistema multi-agent automatizzato per penetration testing completo

MODALITÀ DISPONIBILI:
1. Modalità Classica: Sistema originale con agenti sequenziali
2. Modalità Collaborativa: Nuovo sistema con agenti AI collaborativi continui

Utilizza CrewAI con ai_client.py come backend LLM
"""

import asyncio
import logging
import os
import re
import sys
from datetime import datetime
from typing import Dict, Any

# Carica variabili d'ambiente dal file .env
try:
    from dotenv import load_dotenv
    load_dotenv()
except ImportError:
    # Se python-dotenv non è installato, prova a caricare manualmente
    if os.path.exists('.env'):
        with open('.env', 'r') as f:
            for line in f:
                if line.strip() and not line.startswith('#'):
                    key, value = line.strip().split('=', 1)
                    os.environ[key] = value

from agents.pentest_crew import PentestCrew
from agents.collaborative_crew import CollaborativeCrew
from agents.ai_client import ai_client
from database.pentest_database import pentest_db
from reports.pdf_generator import pdf_generator


class PentestManager:
    """
    Manager principale per orchestrare i multi-agent di penetration testing automatizzato
    Supporta modalità classica e collaborativa
    """

    def __init__(self, mode: str = "classic"):
        self.setup_logging()
        self.logger = logging.getLogger("heka.pentest_manager")
        self.mode = mode

        if mode == "collaborative":
            self.crew = CollaborativeCrew()
            self.logger.info("Modalità collaborativa attivata")
        else:
            self.crew = PentestCrew()
            self.logger.info("Modalità classica attivata")

        self.current_session_id = None
        self.current_target_id = None

    def setup_logging(self):
        """Configura il logging"""
        logging.basicConfig(
            level=logging.INFO,
            format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
            handlers=[
                logging.FileHandler('logs/pentest_manager.log'),
                logging.StreamHandler()
            ]
        )

        # Crea directory logs se non esiste
        os.makedirs('logs', exist_ok=True)
        os.makedirs('reports', exist_ok=True)

    def validate_target(self, target: str) -> bool:
        """Valida il formato del target"""
        # IP address pattern
        ip_pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'

        # Domain pattern
        domain_pattern = r'^(?:[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?\.)*[a-zA-Z0-9](?:[a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?$'

        # CIDR pattern
        cidr_pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\/(?:[0-9]|[1-2][0-9]|3[0-2])$'

        return (re.match(ip_pattern, target) or
                re.match(domain_pattern, target) or
                re.match(cidr_pattern, target))

    def get_mode_selection(self) -> str:
        """Seleziona la modalità di penetration testing"""
        print("\n🛡️  HEKA PENETRATION TESTING SYSTEM")
        print("=" * 60)
        print("Sistema multi-agent automatizzato per penetration testing")
        print("=" * 60)

        print("\n🔧 Seleziona modalità di penetration testing:")
        print("   1. 🔄 MODALITÀ COLLABORATIVA (Raccomandato)")
        print("      • Agenti AI che si scambiano informazioni continuamente")
        print("      • Ciclo continuo fino al completamento dell'obiettivo")
        print("      • Web Intelligence con dorks personalizzate")
        print("      • Analisi collaborativa dei risultati")
        print("      • Esecuzione comandi guidata da AI")
        print()
        print("   2. 📋 MODALITÀ CLASSICA")
        print("      • Esecuzione sequenziale degli agenti")
        print("      • Processo strutturato tradizionale")
        print("      • Report dettagliato finale")

        while True:
            choice = input("\n🔧 Scegli modalità (1 o 2): ").strip()

            if choice == "1":
                return "collaborative"
            elif choice == "2":
                return "classic"
            else:
                print("❌ Scelta non valida! Inserisci 1 o 2.")

    def get_target_input(self) -> str:
        """Richiede input del target all'utente"""
        print("\n" + "="*60)
        print("🛡️  HEKA PENETRATION TESTING SYSTEM")
        print("="*60)
        print("\nInserisci il target da testare:")
        print("• Indirizzo IP (es: *************)")
        print("• Dominio (es: example.com)")
        print("• Range CIDR (es: ***********/24)")
        print("\nDigita 'exit' per uscire")

        while True:
            target = input("\n🎯 Target: ").strip()

            if target.lower() == 'exit':
                print("Uscita dal sistema.")
                sys.exit(0)

            if not target:
                print("❌ Target non può essere vuoto. Riprova.")
                continue

            if self.validate_target(target):
                return target
            else:
                print("❌ Formato target non valido. Riprova.")
                print("   Esempi validi: *************, example.com, ***********/24")

    def determine_target_type(self, target: str) -> str:
        """Determina il tipo di target"""
        ip_pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$'
        cidr_pattern = r'^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\/(?:[0-9]|[1-2][0-9]|3[0-2])$'

        if re.match(ip_pattern, target):
            return "ip"
        elif re.match(cidr_pattern, target):
            return "range"
        else:
            return "domain"

    def get_objective_input(self) -> str:
        """Ottiene l'obiettivo del penetration test per modalità collaborativa"""
        print("\n🎯 Definisci l'obiettivo del penetration test:")
        print("   Esempi:")
        print("   • Trova vulnerabilità web nel target")
        print("   • Ottieni accesso al sistema target")
        print("   • Identifica servizi esposti e vulnerabilità")
        print("   • Testa la sicurezza dell'applicazione web")

        while True:
            objective = input("\n🎯 Obiettivo: ").strip()
            if objective:
                return objective
            print("❌ L'obiettivo non può essere vuoto!")

    async def run_collaborative_pentest(self, target: str, objective: str) -> Dict[str, Any]:
        """
        Esegue penetration test collaborativo continuo
        """
        start_time = datetime.now()

        try:
            self.logger.info(f"Avvio penetration test collaborativo su {target}")

            # 1. Determina tipo di target
            target_type = self.determine_target_type(target)

            # 2. Crea target e sessione nel database
            target_id = pentest_db.create_target(target, target_type, f"Collaborative pentest - {objective}")
            session_name = f"Collaborative_Pentest_{target}_{start_time.strftime('%Y%m%d_%H%M%S')}"
            session_id = pentest_db.create_pentest_session(target_id, session_name, objective)

            self.current_target_id = target_id
            self.current_session_id = session_id
            self.crew.session_id = session_id

            print(f"\n📊 Sessione collaborativa creata: {session_name}")
            print(f"🎯 Target: {target} ({target_type})")
            print(f"🎯 Obiettivo: {objective}")
            print(f"🕐 Inizio: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print("\n" + "="*60)
            print("🤖 Avvio agenti collaborativi...")
            print("="*60)

            # 3. Esegui penetration test collaborativo
            results = await self.crew.execute_collaborative_pentest(target, objective)

            # 4. Genera report PDF
            print("\n📄 Generazione report PDF...")
            session_data = pentest_db.get_session_summary(session_id)
            report_path = f"reports/collaborative_pentest_report_{session_name}.pdf"
            pdf_generator.generate_pentest_report(session_data, report_path)

            # 5. Completa sessione
            pentest_db.complete_pentest_session(session_id, results['status'], report_path)

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            # 6. Compila risultati finali
            final_results = {
                "target": target,
                "target_type": target_type,
                "objective": objective,
                "session_id": session_id,
                "session_name": session_name,
                "start_time": start_time.isoformat(),
                "end_time": end_time.isoformat(),
                "duration_seconds": duration,
                "status": results['status'],
                "progress": results['progress'],
                "iterations": results['iterations'],
                "findings": results['findings'],
                "agents_involved": results['agents_involved'],
                "total_messages": results['total_messages'],
                "report_path": report_path
            }

            self.logger.info(f"Penetration test completato: {results['status']}")
            return final_results

        except Exception as e:
            self.logger.error(f"Errore durante penetration test: {e}")
            if self.current_session_id:
                pentest_db.complete_pentest_session(self.current_session_id, "failed")

            return {
                "status": "failed",
                "error": str(e),
                "target": target,
                "objective": objective
            }

    async def run_automated_pentest(self, target: str) -> Dict[str, Any]:
        """
        Esegue penetration test automatizzato completo

        Args:
            target: Target da testare

        Returns:
            Risultati completi del penetration test
        """
        start_time = datetime.now()
        self.logger.info(f"🚀 Avvio penetration test automatizzato su: {target}")

        try:
            # 1. Crea target nel database
            target_type = self.determine_target_type(target)
            target_id = pentest_db.create_target(
                target_name=target,
                target_type=target_type,
                description=f"Penetration test automatizzato su {target}"
            )
            self.current_target_id = target_id

            # 2. Crea sessione di penetration testing
            session_name = f"Auto-Pentest-{datetime.now().strftime('%Y%m%d-%H%M%S')}"
            session_id = pentest_db.create_pentest_session(
                target_id=target_id,
                session_name=session_name,
                objective=f"Identificare vulnerabilità sfruttabili su {target}"
            )
            self.current_session_id = session_id

            print(f"\n📊 Sessione creata: {session_name}")
            print(f"🎯 Target: {target} ({target_type})")
            print(f"🕐 Inizio: {start_time.strftime('%Y-%m-%d %H:%M:%S')}")
            print("\n" + "="*60)

            # 3. Esegui fasi del penetration test
            results = await self._execute_pentest_phases(target, session_id)

            # 4. Genera report PDF
            print("\n📄 Generazione report PDF...")
            session_data = pentest_db.get_session_summary(session_id)
            report_path = f"reports/pentest_report_{session_name}.pdf"
            pdf_generator.generate_pentest_report(session_data, report_path)

            # 5. Completa sessione
            pentest_db.complete_pentest_session(session_id, "completed", report_path)

            end_time = datetime.now()
            duration = (end_time - start_time).total_seconds()

            print(f"\n✅ Penetration test completato!")
            print(f"⏱️  Durata totale: {duration/60:.1f} minuti")
            print(f"📄 Report salvato: {report_path}")

            return {
                "session_id": session_id,
                "target": target,
                "status": "completed",
                "duration_seconds": duration,
                "report_path": report_path,
                "results": results
            }

        except Exception as e:
            self.logger.error(f"Errore durante penetration test: {e}")
            if self.current_session_id:
                pentest_db.complete_pentest_session(self.current_session_id, "failed")

            print(f"\n❌ Errore durante penetration test: {e}")
            return {
                "target": target,
                "status": "failed",
                "error": str(e)
            }

    async def _execute_pentest_phases(self, target: str, session_id: int) -> Dict[str, Any]:
        """Esegue le fasi del penetration test in sequenza"""
        results = {}

        # Fase 1: Reconnaissance
        print("\n🔍 FASE 1: Reconnaissance")
        print("-" * 40)
        recon_results = await self._execute_reconnaissance(target, session_id)
        results['reconnaissance'] = recon_results

        # Fase 2: Web Intelligence
        print("\n🌐 FASE 2: Web Intelligence")
        print("-" * 40)
        web_intel_results = await self._execute_web_intelligence(target, session_id)
        results['web_intelligence'] = web_intel_results

        # Fase 3: Vulnerability Assessment
        print("\n🔒 FASE 3: Vulnerability Assessment")
        print("-" * 40)
        vuln_results = await self._execute_vulnerability_assessment(target, session_id)
        results['vulnerability_assessment'] = vuln_results

        # Fase 4: Exploitation
        print("\n💥 FASE 4: Exploitation")
        print("-" * 40)
        exploit_results = await self._execute_exploitation(target, session_id)
        results['exploitation'] = exploit_results

        return results

    async def _execute_reconnaissance(self, target: str, session_id: int) -> Dict[str, Any]:
        """Esegue la fase di reconnaissance"""
        try:
            # Crea task nel database
            task_id = pentest_db.create_agent_task(
                session_id=session_id,
                agent_name="reconnaissance",
                task_description=f"Reconnaissance su {target}",
                task_type="reconnaissance"
            )

            print(f"🔍 Avvio reconnaissance su {target}...")

            # Esegui reconnaissance con il crew
            result = self.crew.execute_reconnaissance(target)

            # Aggiorna task nel database
            pentest_db.update_agent_task(task_id, "completed", str(result))

            print("✅ Reconnaissance completato")
            return result

        except Exception as e:
            self.logger.error(f"Errore durante reconnaissance: {e}")
            if 'task_id' in locals():
                pentest_db.update_agent_task(task_id, "failed", f"Errore: {str(e)}")
            return {"status": "failed", "error": str(e)}

    async def _execute_web_intelligence(self, target: str, session_id: int) -> Dict[str, Any]:
        """Esegue la fase di web intelligence"""
        try:
            # Crea task nel database
            task_id = pentest_db.create_agent_task(
                session_id=session_id,
                agent_name="web_intelligence",
                task_description=f"Web Intelligence su {target}",
                task_type="web_intelligence"
            )

            print(f"🌐 Avvio web intelligence su {target}...")

            # Esegui web intelligence con il crew
            result = self.crew.execute_web_intelligence(f"Ricerca informazioni su {target}")

            # Aggiorna task nel database
            pentest_db.update_agent_task(task_id, "completed", str(result))

            print("✅ Web Intelligence completato")
            return result

        except Exception as e:
            self.logger.error(f"Errore durante web intelligence: {e}")
            if 'task_id' in locals():
                pentest_db.update_agent_task(task_id, "failed", f"Errore: {str(e)}")
            return {"status": "failed", "error": str(e)}

    async def _execute_vulnerability_assessment(self, target: str, session_id: int) -> Dict[str, Any]:
        """Esegue la fase di vulnerability assessment"""
        try:
            # Crea task nel database
            task_id = pentest_db.create_agent_task(
                session_id=session_id,
                agent_name="vulnerability_assessment",
                task_description=f"Vulnerability Assessment su {target}",
                task_type="vulnerability_assessment"
            )

            print(f"🔒 Avvio vulnerability assessment su {target}...")

            # Esegui vulnerability assessment con il crew
            result = self.crew.execute_vulnerability_assessment(target)

            # Aggiorna task nel database
            pentest_db.update_agent_task(task_id, "completed", str(result))

            print("✅ Vulnerability Assessment completato")
            return result

        except Exception as e:
            self.logger.error(f"Errore durante vulnerability assessment: {e}")
            if 'task_id' in locals():
                pentest_db.update_agent_task(task_id, "failed", f"Errore: {str(e)}")
            return {"status": "failed", "error": str(e)}

    async def _execute_exploitation(self, target: str, session_id: int) -> Dict[str, Any]:
        """Esegue la fase di exploitation"""
        try:
            # Crea task nel database
            task_id = pentest_db.create_agent_task(
                session_id=session_id,
                agent_name="exploitation",
                task_description=f"Exploitation su {target}",
                task_type="exploitation"
            )

            print(f"💥 Avvio exploitation su {target}...")

            # Esegui exploitation con il crew
            result = self.crew.execute_exploitation(target)

            # Aggiorna task nel database
            pentest_db.update_agent_task(task_id, "completed", str(result))

            print("✅ Exploitation completato")
            return result

        except Exception as e:
            self.logger.error(f"Errore durante exploitation: {e}")
            if 'task_id' in locals():
                pentest_db.update_agent_task(task_id, "failed", f"Errore: {str(e)}")
            return {"status": "failed", "error": str(e)}

async def main():
    """Funzione principale"""
    # Prima seleziona la modalità
    temp_manager = PentestManager()
    mode = temp_manager.get_mode_selection()

    # Crea manager con modalità selezionata
    manager = PentestManager(mode=mode)

    try:
        # Inizializza il sistema
        await ai_client.start()

        # Ottieni target dall'utente
        target = manager.get_target_input()

        if mode == "collaborative":
            # Modalità collaborativa
            objective = manager.get_objective_input()
            results = await manager.run_collaborative_pentest(target, objective)

            # Mostra risultati collaborativi
            print("\n" + "="*60)
            print("📋 RISULTATI PENETRATION TEST COLLABORATIVO")
            print("="*60)
            print(f"🎯 Target: {results.get('target', 'N/A')}")
            print(f"🎯 Obiettivo: {results.get('objective', 'N/A')}")
            print(f"📊 Stato: {results.get('status', 'N/A')}")
            print(f"📈 Progresso: {results.get('progress', 0):.1%}")
            print(f"🔄 Iterazioni: {results.get('iterations', 0)}")
            print(f"🤖 Agenti coinvolti: {', '.join(results.get('agents_involved', []))}")
            print(f"💬 Messaggi scambiati: {results.get('total_messages', 0)}")

            if results.get('duration_seconds'):
                duration_min = results['duration_seconds'] / 60
                if duration_min < 1:
                    print(f"⏱️  Durata: {results['duration_seconds']:.1f} secondi")
                else:
                    print(f"⏱️  Durata: {duration_min:.1f} minuti")

            if results.get('report_path'):
                print(f"📄 Report: {results['report_path']}")

            # Mostra findings principali
            findings = results.get('findings', [])
            if findings:
                print(f"\n🔍 Findings principali ({len(findings)}):")
                for i, finding in enumerate(findings[-5:], 1):  # Ultimi 5
                    finding_type = finding.get('type', 'unknown')
                    content = str(finding.get('content', ''))[:100]
                    print(f"   {i}. {finding_type}: {content}...")

            if results.get('status') == 'failed':
                print(f"\n❌ Errore: {results.get('error', 'Errore sconosciuto')}")
            elif results.get('status') == 'completed':
                print(f"\n✅ Penetration test completato con successo!")
            else:
                print(f"\n⚠️  Penetration test interrotto")
        else:
            # Modalità classica
            results = await manager.run_automated_pentest(target)

            # Mostra risultati classici
            print("\n" + "="*60)
            print("📋 RISULTATI FINALI")
            print("="*60)
            print(f"Target: {results.get('target', 'N/A')}")
            print(f"Stato: {results.get('status', 'N/A')}")
            if results.get('duration_seconds'):
                duration_min = results['duration_seconds']/60
                if duration_min < 1:
                    print(f"Durata: {results['duration_seconds']:.1f} secondi")
                else:
                    print(f"Durata: {duration_min:.1f} minuti")
            if results.get('report_path'):
                print(f"Report: {results['report_path']}")

    except KeyboardInterrupt:
        print("\n\n⚠️  Interruzione da utente. Chiusura sistema...")
    except Exception as e:
        print(f"\n❌ Errore critico: {e}")
        import traceback
        traceback.print_exc()
    finally:
        try:
            await ai_client.stop()
        except:
            pass


if __name__ == "__main__":
    asyncio.run(main())