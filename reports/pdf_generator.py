#!/usr/bin/env python3
"""
PDF Report Generator
Sistema per generare report dettagliati in formato PDF dei penetration test
"""

import os
import logging
from datetime import datetime
from typing import Dict, Any, List
from reportlab.lib.pagesizes import letter, A4
from reportlab.lib.styles import getSampleStyleSheet, ParagraphStyle
from reportlab.lib.units import inch
from reportlab.lib.colors import HexColor, black, red, orange, yellow, green
from reportlab.platypus import SimpleDocTemplate, Paragraph, Spacer, Table, TableStyle, PageBreak
from reportlab.platypus import Image as ReportLabImage
from reportlab.lib.enums import TA_CENTER, TA_LEFT, TA_RIGHT, TA_JUSTIFY


class PentestPDFGenerator:
    """Generatore di report PDF per penetration testing"""
    
    def __init__(self):
        self.logger = logging.getLogger("heka.pdf_generator")
        self.styles = getSampleStyleSheet()
        self._setup_custom_styles()
        
        # Colori per severità vulnerabilità
        self.severity_colors = {
            'critical': HexColor('#8B0000'),  # Dark red
            'high': HexColor('#FF4500'),      # Orange red
            'medium': HexColor('#FFA500'),    # Orange
            'low': HexColor('#FFD700'),       # Gold
            'info': HexColor('#87CEEB')       # Sky blue
        }
    
    def _setup_custom_styles(self):
        """Configura stili personalizzati"""
        # Titolo principale (solo se non esiste già)
        if 'CustomTitle' not in self.styles:
            self.styles.add(ParagraphStyle(
                name='CustomTitle',
                parent=self.styles['Title'],
                fontSize=24,
                spaceAfter=30,
                alignment=TA_CENTER,
                textColor=HexColor('#2E4057')
            ))

        # Sottotitolo (solo se non esiste già)
        if 'CustomHeading1' not in self.styles:
            self.styles.add(ParagraphStyle(
                name='CustomHeading1',
                parent=self.styles['Heading1'],
                fontSize=18,
                spaceAfter=12,
                textColor=HexColor('#2E4057'),
                borderWidth=1,
                borderColor=HexColor('#2E4057'),
                borderPadding=5
            ))

        # Sezione (solo se non esiste già)
        if 'CustomHeading2' not in self.styles:
            self.styles.add(ParagraphStyle(
                name='CustomHeading2',
                parent=self.styles['Heading2'],
                fontSize=14,
                spaceAfter=10,
                textColor=HexColor('#34495E')
            ))

        # Vulnerabilità critica (solo se non esiste già)
        if 'CriticalVuln' not in self.styles:
            self.styles.add(ParagraphStyle(
                name='CriticalVuln',
                parent=self.styles['Normal'],
                fontSize=12,
                textColor=HexColor('#8B0000'),
                backColor=HexColor('#FFE4E1'),
                borderWidth=1,
                borderColor=HexColor('#8B0000'),
                borderPadding=5
            ))
        
        # Codice/comando (solo se non esiste già)
        if 'Code' not in self.styles:
            self.styles.add(ParagraphStyle(
                name='Code',
                parent=self.styles['Normal'],
                fontSize=10,
                fontName='Courier',
                backColor=HexColor('#F5F5F5'),
                borderWidth=1,
                borderColor=HexColor('#CCCCCC'),
                borderPadding=5,
                leftIndent=10,
                rightIndent=10
            ))
    
    def generate_pentest_report(self, session_data: Dict[str, Any], output_path: str) -> str:
        """
        Genera report completo di penetration testing
        
        Args:
            session_data: Dati della sessione dal database
            output_path: Path dove salvare il PDF
            
        Returns:
            Path del file PDF generato
        """
        try:
            # Crea directory se non esiste
            os.makedirs(os.path.dirname(output_path), exist_ok=True)
            
            # Crea documento PDF
            doc = SimpleDocTemplate(
                output_path,
                pagesize=A4,
                rightMargin=72,
                leftMargin=72,
                topMargin=72,
                bottomMargin=18
            )
            
            # Costruisci contenuto
            story = []
            
            # Pagina di copertina
            story.extend(self._build_cover_page(session_data))
            story.append(PageBreak())
            
            # Executive Summary
            story.extend(self._build_executive_summary(session_data))
            story.append(PageBreak())
            
            # Metodologia
            story.extend(self._build_methodology_section(session_data))
            story.append(PageBreak())
            
            # Risultati dettagliati
            story.extend(self._build_detailed_results(session_data))
            story.append(PageBreak())
            
            # Vulnerabilità
            story.extend(self._build_vulnerabilities_section(session_data))
            story.append(PageBreak())
            
            # Timeline delle attività
            story.extend(self._build_timeline_section(session_data))
            story.append(PageBreak())
            
            # Raccomandazioni
            story.extend(self._build_recommendations_section(session_data))
            story.append(PageBreak())
            
            # Appendici
            story.extend(self._build_appendices(session_data))
            
            # Genera PDF
            doc.build(story)
            
            self.logger.info(f"Report PDF generato: {output_path}")
            return output_path
            
        except Exception as e:
            self.logger.error(f"Errore generazione PDF: {e}")
            raise
    
    def _build_cover_page(self, session_data: Dict[str, Any]) -> List:
        """Costruisce la pagina di copertina"""
        story = []
        session = session_data['session']
        
        # Logo/Titolo
        story.append(Spacer(1, 2*inch))
        story.append(Paragraph("🛡️ HEKA PENETRATION TESTING", self.styles['CustomTitle']))
        story.append(Spacer(1, 0.5*inch))
        
        # Informazioni sessione
        story.append(Paragraph("REPORT DI SICUREZZA", self.styles['CustomHeading1']))
        story.append(Spacer(1, 0.3*inch))
        
        # Tabella informazioni
        data = [
            ['Target:', session.get('target_name', 'N/A')],
            ['Sessione:', session.get('session_name', 'N/A')],
            ['Data inizio:', session.get('start_time', 'N/A')],
            ['Data fine:', session.get('end_time', 'N/A')],
            ['Stato:', session.get('status', 'N/A')],
            ['Obiettivo:', session.get('objective', 'N/A')]
        ]
        
        table = Table(data, colWidths=[2*inch, 4*inch])
        table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (0, -1), HexColor('#E8E8E8')),
            ('TEXTCOLOR', (0, 0), (-1, -1), black),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, -1), 'Helvetica'),
            ('FONTSIZE', (0, 0), (-1, -1), 12),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
            ('BACKGROUND', (1, 0), (1, -1), HexColor('#F8F8F8')),
            ('GRID', (0, 0), (-1, -1), 1, black)
        ]))
        
        story.append(table)
        story.append(Spacer(1, 1*inch))
        
        # Disclaimer
        disclaimer = """
        <b>CONFIDENZIALE</b><br/>
        Questo documento contiene informazioni riservate relative alla sicurezza del sistema testato.
        La distribuzione è limitata al personale autorizzato.
        """
        story.append(Paragraph(disclaimer, self.styles['Normal']))
        
        return story
    
    def _build_executive_summary(self, session_data: Dict[str, Any]) -> List:
        """Costruisce l'executive summary"""
        story = []
        
        story.append(Paragraph("Executive Summary", self.styles['CustomHeading1']))
        
        # Statistiche generali
        task_stats = session_data.get('task_statistics', {})
        vuln_stats = session_data.get('vulnerability_statistics', {})
        
        summary_text = f"""
        Durante questa sessione di penetration testing sono state eseguite <b>{task_stats.get('total_tasks', 0)}</b> 
        attività di testing utilizzando un approccio multi-agent automatizzato.
        
        <br/><br/>
        
        <b>Risultati principali:</b><br/>
        • Task completate: {task_stats.get('completed_tasks', 0)}/{task_stats.get('total_tasks', 0)}<br/>
        • Vulnerabilità totali trovate: {sum(vuln_stats.values()) if vuln_stats else 0}<br/>
        • Vulnerabilità critiche: {vuln_stats.get('critical', 0)}<br/>
        • Vulnerabilità high: {vuln_stats.get('high', 0)}<br/>
        • Vulnerabilità medium: {vuln_stats.get('medium', 0)}<br/>
        • Vulnerabilità low: {vuln_stats.get('low', 0)}
        """
        
        story.append(Paragraph(summary_text, self.styles['Normal']))
        story.append(Spacer(1, 0.3*inch))
        
        # Grafico vulnerabilità (tabella semplificata)
        if vuln_stats:
            story.append(Paragraph("Distribuzione Vulnerabilità per Severità", self.styles['CustomHeading2']))
            
            vuln_data = [['Severità', 'Numero', 'Percentuale']]
            total_vulns = sum(vuln_stats.values())
            
            for severity in ['critical', 'high', 'medium', 'low', 'info']:
                count = vuln_stats.get(severity, 0)
                percentage = (count / total_vulns * 100) if total_vulns > 0 else 0
                vuln_data.append([severity.upper(), str(count), f"{percentage:.1f}%"])
            
            vuln_table = Table(vuln_data, colWidths=[2*inch, 1*inch, 1*inch])
            vuln_table.setStyle(TableStyle([
                ('BACKGROUND', (0, 0), (-1, 0), HexColor('#4472C4')),
                ('TEXTCOLOR', (0, 0), (-1, 0), HexColor('#FFFFFF')),
                ('ALIGN', (0, 0), (-1, -1), 'CENTER'),
                ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
                ('FONTSIZE', (0, 0), (-1, -1), 10),
                ('BOTTOMPADDING', (0, 0), (-1, -1), 12),
                ('GRID', (0, 0), (-1, -1), 1, black)
            ]))
            
            story.append(vuln_table)
        
        return story
    
    def _build_methodology_section(self, session_data: Dict[str, Any]) -> List:
        """Costruisce la sezione metodologia"""
        story = []
        
        story.append(Paragraph("Metodologia", self.styles['CustomHeading1']))
        
        methodology_text = """
        Il penetration testing è stato condotto utilizzando il framework Heka, un sistema multi-agent 
        automatizzato che coordina diversi agenti specializzati:
        
        <br/><br/>
        
        <b>1. Reconnaissance Agent</b><br/>
        • Raccolta informazioni OSINT<br/>
        • Enumerazione servizi e porte<br/>
        • Identificazione tecnologie utilizzate<br/>
        
        <br/>
        
        <b>2. Web Intelligence Agent</b><br/>
        • Ricerca vulnerabilità note<br/>
        • Analisi configurazioni pubbliche<br/>
        • Correlazione con database CVE<br/>
        
        <br/>
        
        <b>3. Kali Executor Agent</b><br/>
        • Esecuzione scan automatizzati<br/>
        • Testing vulnerabilità<br/>
        • Validazione exploit<br/>
        
        <br/>
        
        <b>4. Exploitation Agent</b><br/>
        • Sfruttamento vulnerabilità<br/>
        • Privilege escalation<br/>
        • Post-exploitation<br/>
        
        <br/>
        
        <b>5. Reporting Agent</b><br/>
        • Consolidamento risultati<br/>
        • Analisi impatto<br/>
        • Generazione raccomandazioni
        """
        
        story.append(Paragraph(methodology_text, self.styles['Normal']))
        
        return story
    
    def _build_detailed_results(self, session_data: Dict[str, Any]) -> List:
        """Costruisce i risultati dettagliati"""
        story = []
        
        story.append(Paragraph("Risultati Dettagliati", self.styles['CustomHeading1']))
        
        tasks = session_data.get('tasks', [])
        
        for task in tasks:
            story.append(Paragraph(f"Task: {task.get('task_type', 'N/A')}", self.styles['CustomHeading2']))
            
            # Dettagli task
            task_details = f"""
            <b>Agente:</b> {task.get('agent_name', 'N/A')}<br/>
            <b>Descrizione:</b> {task.get('task_description', 'N/A')}<br/>
            <b>Comando:</b> {task.get('command_executed', 'N/A')}<br/>
            <b>Stato:</b> {task.get('status', 'N/A')}<br/>
            <b>Durata:</b> {self._format_duration(task.get('start_time'), task.get('end_time'))}<br/>
            <b>Vulnerabilità trovate:</b> {task.get('vulnerabilities_found', 0)}
            """
            
            story.append(Paragraph(task_details, self.styles['Normal']))
            
            # Risultato
            if task.get('result_summary'):
                story.append(Paragraph("Risultato:", self.styles['CustomHeading2']))
                story.append(Paragraph(task['result_summary'], self.styles['Normal']))
            
            story.append(Spacer(1, 0.2*inch))
        
        return story
    
    def _build_vulnerabilities_section(self, session_data: Dict[str, Any]) -> List:
        """Costruisce la sezione vulnerabilità"""
        story = []
        
        story.append(Paragraph("Vulnerabilità Identificate", self.styles['CustomHeading1']))
        
        vulnerabilities = session_data.get('vulnerabilities', [])
        
        if not vulnerabilities:
            story.append(Paragraph("Nessuna vulnerabilità identificata.", self.styles['Normal']))
            return story
        
        # Raggruppa per severità
        vuln_by_severity = {}
        for vuln in vulnerabilities:
            severity = vuln.get('severity', 'info')
            if severity not in vuln_by_severity:
                vuln_by_severity[severity] = []
            vuln_by_severity[severity].append(vuln)
        
        # Mostra vulnerabilità per severità
        for severity in ['critical', 'high', 'medium', 'low', 'info']:
            if severity in vuln_by_severity:
                story.append(Paragraph(f"Vulnerabilità {severity.upper()}", self.styles['CustomHeading2']))
                
                for vuln in vuln_by_severity[severity]:
                    story.extend(self._format_vulnerability(vuln))
                    story.append(Spacer(1, 0.2*inch))
        
        return story
    
    def _format_vulnerability(self, vuln: Dict[str, Any]) -> List:
        """Formatta una singola vulnerabilità"""
        story = []
        
        # Titolo vulnerabilità
        title = f"🚨 {vuln.get('title', 'Vulnerabilità')}"
        if vuln.get('cve_id'):
            title += f" ({vuln['cve_id']})"
        
        story.append(Paragraph(title, self.styles['CustomHeading2']))
        
        # Dettagli
        details = f"""
        <b>Tipo:</b> {vuln.get('vulnerability_type', 'N/A')}<br/>
        <b>Severità:</b> {vuln.get('severity', 'N/A').upper()}<br/>
        <b>Servizio:</b> {vuln.get('affected_service', 'N/A')}<br/>
        <b>Porta:</b> {vuln.get('port', 'N/A')}<br/>
        <b>CVSS Score:</b> {vuln.get('cvss_score', 'N/A')}
        """
        
        story.append(Paragraph(details, self.styles['Normal']))
        
        # Descrizione
        if vuln.get('description'):
            story.append(Paragraph("<b>Descrizione:</b>", self.styles['Normal']))
            story.append(Paragraph(vuln['description'], self.styles['Normal']))
        
        # Proof of Concept
        if vuln.get('proof_of_concept'):
            story.append(Paragraph("<b>Proof of Concept:</b>", self.styles['Normal']))
            story.append(Paragraph(vuln['proof_of_concept'], self.styles['Code']))
        
        # Remediation
        if vuln.get('remediation'):
            story.append(Paragraph("<b>Remediation:</b>", self.styles['Normal']))
            story.append(Paragraph(vuln['remediation'], self.styles['Normal']))
        
        return story
    
    def _build_timeline_section(self, session_data: Dict[str, Any]) -> List:
        """Costruisce la timeline delle attività"""
        story = []
        
        story.append(Paragraph("Timeline delle Attività", self.styles['CustomHeading1']))
        
        tasks = session_data.get('tasks', [])
        communications = session_data.get('communications', [])
        
        # Combina e ordina eventi
        events = []
        
        for task in tasks:
            events.append({
                'timestamp': task.get('start_time'),
                'type': 'task_start',
                'description': f"Avvio task: {task.get('task_description', 'N/A')} ({task.get('agent_name', 'N/A')})"
            })
            
            if task.get('end_time'):
                events.append({
                    'timestamp': task.get('end_time'),
                    'type': 'task_end',
                    'description': f"Completamento task: {task.get('task_description', 'N/A')} - {task.get('status', 'N/A')}"
                })
        
        for comm in communications:
            events.append({
                'timestamp': comm.get('timestamp'),
                'type': 'communication',
                'description': f"{comm.get('from_agent', 'N/A')} → {comm.get('to_agent', 'N/A')}: {comm.get('message_type', 'N/A')}"
            })
        
        # Ordina per timestamp
        events.sort(key=lambda x: x.get('timestamp', ''))
        
        # Crea tabella timeline
        timeline_data = [['Timestamp', 'Evento']]
        for event in events[:50]:  # Limita a 50 eventi
            timestamp = self._format_timestamp(event.get('timestamp'))
            timeline_data.append([timestamp, event.get('description', 'N/A')])
        
        timeline_table = Table(timeline_data, colWidths=[2*inch, 4*inch])
        timeline_table.setStyle(TableStyle([
            ('BACKGROUND', (0, 0), (-1, 0), HexColor('#4472C4')),
            ('TEXTCOLOR', (0, 0), (-1, 0), HexColor('#FFFFFF')),
            ('ALIGN', (0, 0), (-1, -1), 'LEFT'),
            ('FONTNAME', (0, 0), (-1, 0), 'Helvetica-Bold'),
            ('FONTSIZE', (0, 0), (-1, -1), 9),
            ('BOTTOMPADDING', (0, 0), (-1, -1), 8),
            ('GRID', (0, 0), (-1, -1), 1, black)
        ]))
        
        story.append(timeline_table)
        
        return story
    
    def _build_recommendations_section(self, session_data: Dict[str, Any]) -> List:
        """Costruisce la sezione raccomandazioni"""
        story = []
        
        story.append(Paragraph("Raccomandazioni", self.styles['CustomHeading1']))
        
        vulnerabilities = session_data.get('vulnerabilities', [])
        
        if not vulnerabilities:
            story.append(Paragraph("Nessuna raccomandazione specifica. Il sistema appare sicuro.", self.styles['Normal']))
            return story
        
        # Raccomandazioni generali
        general_recommendations = """
        <b>Raccomandazioni Generali:</b><br/>
        1. Implementare un programma di vulnerability management<br/>
        2. Eseguire penetration testing regolari<br/>
        3. Mantenere aggiornati tutti i sistemi e software<br/>
        4. Implementare monitoring e logging avanzati<br/>
        5. Formare il personale sulla sicurezza informatica
        """
        
        story.append(Paragraph(general_recommendations, self.styles['Normal']))
        story.append(Spacer(1, 0.3*inch))
        
        # Raccomandazioni specifiche per vulnerabilità critiche/high
        critical_vulns = [v for v in vulnerabilities if v.get('severity') in ['critical', 'high']]
        
        if critical_vulns:
            story.append(Paragraph("Azioni Immediate Richieste:", self.styles['CustomHeading2']))
            
            for i, vuln in enumerate(critical_vulns[:10], 1):
                if vuln.get('remediation'):
                    story.append(Paragraph(f"{i}. {vuln['remediation']}", self.styles['Normal']))
        
        return story
    
    def _build_appendices(self, session_data: Dict[str, Any]) -> List:
        """Costruisce le appendici"""
        story = []
        
        story.append(Paragraph("Appendici", self.styles['CustomHeading1']))
        
        # Appendice A: Raw Output
        story.append(Paragraph("Appendice A: Output Dettagliato dei Tool", self.styles['CustomHeading2']))
        
        tasks = session_data.get('tasks', [])
        for task in tasks[:5]:  # Limita a 5 task
            if task.get('raw_output'):
                story.append(Paragraph(f"Task: {task.get('task_description', 'N/A')}", self.styles['Normal']))
                # Limita output per evitare PDF troppo grandi
                raw_output = task['raw_output'][:2000] + "..." if len(task['raw_output']) > 2000 else task['raw_output']
                story.append(Paragraph(raw_output, self.styles['Code']))
                story.append(Spacer(1, 0.2*inch))
        
        return story
    
    def _format_duration(self, start_time: str, end_time: str) -> str:
        """Formatta la durata tra due timestamp"""
        try:
            if not start_time or not end_time:
                return "N/A"
            
            start = datetime.fromisoformat(start_time.replace('Z', '+00:00'))
            end = datetime.fromisoformat(end_time.replace('Z', '+00:00'))
            duration = end - start
            
            total_seconds = int(duration.total_seconds())
            minutes = total_seconds // 60
            seconds = total_seconds % 60
            
            return f"{minutes}m {seconds}s"
        except:
            return "N/A"
    
    def _format_timestamp(self, timestamp: str) -> str:
        """Formatta timestamp per visualizzazione"""
        try:
            if not timestamp:
                return "N/A"
            
            dt = datetime.fromisoformat(timestamp.replace('Z', '+00:00'))
            return dt.strftime("%H:%M:%S")
        except:
            return "N/A"


# Istanza globale del generatore PDF
pdf_generator = PentestPDFGenerator()
