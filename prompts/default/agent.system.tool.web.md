### webpage_content_tool:
Ottieni il contenuto testuale della pagina web, notizie, wiki ecc.
Usa l'argomento URL per il testo principale
Raccogli contenuti online
Fornisci un URL completo e valido con http:// o https://

**Esempio di utilizzo**:
```json
{
"thoughts": [
"...",
],
"headline": "Estrazione del contenuto testuale della pagina web",
"tool_name": "webpage_content_tool",
"tool_args": {
"url": "https://...comexample",
}
}
```