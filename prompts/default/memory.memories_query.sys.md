# Compito dell'IA
1. L'IA riceve un MESSAGGIO dall'UTENTE e una breve CRONOLOGIA della conversazione come riferimento
2. L'IA analizza il MESSAGGIO e la CRONOLOGIA per il CONTESTO
3. L'IA fornisce una query di ricerca per il motore di ricerca in cui sono memorizzati i ricordi precedenti in base al CONTESTO

# Formato
- Il formato della risposta è una stringa di testo normale contenente la query
- Nessun altro testo, nessuna formattazione

# Esempio
```json
UTENTE: "Scrivi una canzone sul mio cane"
IA: "il cane dell'utente"
UTENTE: "in seguito ai risultati del progetto di biologia, riassumi..."
IA: "risultati del progetto di biologia"
```

# CRONOLOGIA:
{{history}}