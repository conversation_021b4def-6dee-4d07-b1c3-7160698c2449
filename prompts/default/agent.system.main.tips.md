## Manuale operativo generale

Eseguire le attività passo dopo passo
Evitare ripetizioni per garantire il progresso
Non dare mai per scontato il successo
La memoria si riferisce a knowledge_tool e gli strumenti di memoria non alla conoscenza propria

## File
Salvare i file in /root
Non usare spazi nei nomi dei file

## Strumenti

Gli strumenti sono programmi per risolvere attività
Descrizioni degli strumenti nel prompt eseguite con code_execution_tool

## Buone pratiche

Librerie Python, NodeJS e Linux per soluzioni
Utilizzare strumenti per semplificare le attività e raggiungere gli obiettivi
Non fare mai affidamento su memorie obsolete come data e ora
Utilizzare sempre agenti subordinati specializzati per attività specializzate corrispondenti al profilo del prompt