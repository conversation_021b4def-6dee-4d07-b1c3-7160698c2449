### browser_open:

Controlla il browser Chromium con stato usando Playwright
Usa con l'argomento URL per aprire una nuova pagina
Tutti gli strumenti del browser restituiscono un DOM semplificato con selettori univoci
Una volta aperta la pagina, usa lo strumento browser_do per interagire.

```json
{
"thoughts": ["Devo inviare..."],
"tool_name": "browser_open",
"tool_args": {
"url": "https://www.example.com"
}
}
```

### browser_do:

Utilizza per compilare i moduli, premi i tasti, clicca i pulsanti, esegui JavaScript.
Gli argomenti sono facoltativi.
L'argomento fill è un array di oggetti con selettore e testo.
L'argomento press è un array di tasti da premere in ordine - Invio, Esc....
L'argomento click è un array di selettori cliccati in ordine.
L'argomento execute è una stringa di codice JavaScript eseguito.
Preferisci sempre cliccare prima sui tag <a> o <button>.
Conferma i campi con Invio o trova il pulsante di invio.
I consensi e i popup potrebbero bloccare la pagina, chiudili.
Utilizza solo i selettori menzionati nell'ultima risposta del browser.
Non ripetere gli stessi passaggi se non funzionano! Trovare soluzioni ai problemi
```json
{
"thoughts": [
"Accesso richiesto...",
"Inserirò nome utente e password, cliccherò su "Ricordami" e invierò."
],
"tool_name": "browser_do",
"tool_args": {
"fill": [
{
"selector": "12l",
"text": "root"
},
{
"selector": "14vs",
"text": "toor"
}
],
"click": ["19c", "65d"]
}
}
```

```json
{
"thoughts": [
"Cerca...",
"Inserirò il campo di ricerca e premerò Invio."
],
"tool_name": "browser_do",
"tool_args": {
"fill": [
{
"selector": "98d",
"text": "example"
}
],
"press": ["Invio"]
}
}
```

```json
{
"thoughts": [
"Interazione standard non possibile, devo eseguire codice personalizzato..."
],
"tool_name": "browser_do",
"tool_args": {
"execute": "const elem = document.querySelector('[data-uid=\"4z\"]'); elem.click();"
}
}
```