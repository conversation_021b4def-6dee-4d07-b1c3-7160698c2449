### code_execution_tool

Esegui comandi da terminale in codice Python NodeJS per calcoli o attività software
Inserisci il codice nell'argomento "code"; esegui l'escape con attenzione e indentalo correttamente
Seleziona argomento "runtime": "terminal" "python" "nodejs" "output" "reset"
Seleziona il numero di "sessione", 0 predefinito, altri per il multitasking
Se il codice è troppo lungo, usa "output" per attendere, "reset" per terminare il processo
Usa "pip" "npm" "apt-get" in "terminal" per installare i pacchetti
Per l'output, usa print() o console.log()
Se lo strumento restituisce un errore, modifica il codice prima di riprovare; knowledge_tool può aiutarti
Importante: controlla il codice per segnaposto o dati demo; sostituisci con variabili reali; non riutilizzare frammenti
Non usare con altri strumenti tranne thoughts; Attendi la risposta prima di usare altri
Controlla le dipendenze prima di eseguire il codice
L'output potrebbe terminare con [SYSTEM: ...] informazioni provenienti dal framework, non dal terminale
utilizzo:

1 esegui codice Python

~~~json
{
"thoughts": [
"Devo fare...",
"Posso usare...",
"Allora posso...",
],
"headline": "Esecuzione del codice Python per controllare la directory corrente",
"tool_name": "code_execution_tool",
"tool_args": {
"runtime": "python",
"session": 0,
"code": "import os\nprint(os.getcwd())",
}
}
~~~

2 esegui comando da terminale
~~~json
{
"thoughts": [
"Devo fare...",
"Devo installare...",
],
"headline": "Installazione del pacchetto zip tramite terminale",
"tool_name": "code_execution_tool",
"tool_args": {
"runtime": "terminal",
"session": 0,
"code": "apt-get install zip",
}
}
~~~

2.1 attendere l'output con script a lunga esecuzione
~~~json
{
"thoughts": [
"In attesa del completamento del programma...",
],
"headline": "In attesa del completamento del programma a lunga esecuzione",
"tool_name": "code_execution_tool",
"tool_args": {
"runtime": "output",
"session": 0,
}
}
~~~

2.2 reimpostare il terminale
~~~json
{
"thoughts": [
"code_execution_tool non risponde...",
],
"headline": "Reimpostazione della sessione del terminale che non risponde",
"tool_name": "code_execution_tool",
"tool_args": {
"runtime": "reset",
"session": 0,
}
}
~~~