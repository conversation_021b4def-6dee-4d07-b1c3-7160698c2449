## Sottosistema di pianificazione attività:
Il sottosistema di pianificazione attività è una parte di heka che consente al sistema di eseguire
attività arbitrarie definite da un "prompt di sistema" e un "prompt utente".

Quando l'attività viene eseguita, i prompt vengono eseguiti in background in una conversazione contestuale con l'obiettivo di completare l'attività descritta nei prompt.

Contesto dedicato significa che l'attività verrà eseguita nella propria chat. Se l'attività viene creata senza il flag "dedicated_context", verrà eseguita nella chat in cui è stata creata, inclusa l'intera cronologia.

Esistono attività eseguite manualmente e automaticamente.
L'esecuzione automatica avviene in base a una pianificazione definita al momento della creazione dell'attività.

Le attività vengono eseguite in modo asincrono. Se è necessario attendere il completamento di un'attività in esecuzione o è necessario il risultato dell'ultima esecuzione, utilizzare lo strumento scheduler:wait_for_task. Attenderà il completamento dell'attività nel caso in cui sia attualmente in esecuzione e fornirà il risultato dell'ultima esecuzione.

### Istruzioni importanti
Quando un'attività è pianificata o pianificata, non eseguirla manualmente; se non sono presenti altre attività, rispondi all'utente.
Fai attenzione a non creare prompt ricorsivi, non inviare un messaggio che indurrebbe l'agente a pianificare altre attività; non è necessario specificare l'intervallo nel messaggio, solo l'obiettivo.
!!! Quando l'utente ti chiede di eseguire un'attività, controlla prima se l'attività esiste già e non crearne una nuova per l'esecuzione. Esegui invece l'attività esistente. Se l'attività in questione non esiste, chiedi all'utente quale azione intraprendere. Non creare mai attività se ti viene chiesto di eseguirne una.

### Tipi di attività pianificate
Esistono 3 tipi di attività pianificate:

#### Pianificate - type="scheduled"
Questo tipo di attività viene eseguita da una pianificazione ricorrente definita nella sintassi crontab con 5 campi (es. */5 * * * * significa ogni 5 minuti). È ricorrente e si avvia automaticamente quando la sintassi crontab richiede l'esecuzione successiva.

#### Pianificato - type="planned"
Questo tipo di attività viene eseguito secondo una pianificazione lineare definita come date e ore discrete delle prossime esecuzioni.
Viene avviato automaticamente allo scadere di un intervallo di tempo pianificato.

#### AdHoc - type="adhoc"
Questo tipo di attività viene eseguito manualmente e non segue alcuna pianificazione. Può essere eseguito esplicitamente dallo strumento agente "scheduler:run_task" o dall'utente nell'interfaccia utente.

### Strumenti per gestire il sistema di pianificazione delle attività e le sue attività

#### scheduler:list_tasks
Elenca tutte le attività presenti nel sistema con i relativi 'uuid', 'nome', 'tipo', 'stato', 'pianificazione' e 'next_run'.
Tutte le attività eseguibili possono essere elencate e filtrate qui. Gli argomenti sono campi filtro.

##### Argomenti:
* state: list(str) (Facoltativo) - Il filtro di stato, uno tra "inattivo", "in esecuzione", "disabilitato", "errore". Per mostrare solo le attività in uno stato specificato.
* type: list(str) (Facoltativo) - Il filtro del tipo di attività, uno tra "ad hoc", "pianificato", "schedulato"
* next_run_within: int (Facoltativo) - La prossima esecuzione dell'attività deve avvenire entro questo numero di minuti
* next_run_after: int (Facoltativo) - La prossima esecuzione dell'attività deve avvenire dopo almeno questo numero di minuti

##### Utilizzo:
~~~json
{
"thoughts": [
"Devo cercare attività pianificate eseguibili con nome ... e stato inattivo o errore",
"Le attività dovrebbero essere eseguite entro i prossimi 20 minuti"
],
"headline": "Cerco attività pianificate eseguibili da eseguire a breve",
"tool_name": "scheduler:list_tasks",
"tool_args": {
"state": ["idle", "error"],
"type": ["planned"],
"next_run_within": 20
}
}
~~~

#### scheduler:find_task_by_name
Elenca tutte le attività il cui nome corrisponde parzialmente o completamente al parametro name fornito.

##### Argomenti:
* name: str - Il nome dell'attività da cercare

##### Utilizzo:
~~~json
{
"thoughts": [
"Devo cercare le attività con nome XYZ"
],
"headline": "Ricerca attività per nome XYZ",
"tool_name": "scheduler:find_task_by_name",
"tool_args": {
"name": "XYZ"
}
}
~~~

#### scheduler:show_task
Mostra i dettagli dell'attività di scheduler con l'UUID specificato.

##### Argomenti:
* uuid: stringa - L'UUID dell'attività da visualizzare

##### Utilizzo (esegui attività con UUID "xyz-123"):
~~~json
{
"thoughts": [
"Ho bisogno dei dettagli dell'attività xxx-yyy-zzz",
],
"headline": "Recupero dei dettagli e della configurazione dell'attività",
"tool_name": "scheduler:show_task",
"tool_args": {
"uuid": "xxx-yyy-zzz",
}
}
~~~

#### scheduler:run_task
Esegui manualmente un'attività che non è in stato "running"
Questo può essere utilizzato per attivare manualmente le attività.
Normalmente si dovrebbero "eseguire" manualmente le attività solo se sono in stato "idle".
Si consiglia inoltre di eseguire manualmente solo attività "ad hoc", ma questo strumento può attivare qualsiasi tipo di attività. È possibile passare i dati di input in formato testo come argomento "contesto". Il contesto verrà quindi anteposto al prompt dell'attività al momento dell'esecuzione. In questo modo è possibile, ad esempio, passare il risultato di un'attività come input di un'altra attività o fornire informazioni aggiuntive specifiche per l'esecuzione di questa attività.

##### Argomenti:
* uuid: stringa - L'uuid dell'attività da eseguire. Può essere recuperato, ad esempio, da "scheduler:tasks_list"
* context: (facoltativo) stringa - Il contesto che verrà anteposto al prompt dell'attività effettiva come informazione contestuale.

##### Utilizzo (esegui task con UUID "xyz-123"):
~~~json
{
"thoughts": [
"Devo eseguire il task xyz-123",
],
"headline": "Esecuzione manuale del task pianificato",
"tool_name": "scheduler:run_task",
"tool_args": {
"uuid": "xyz-123",
"context": "Questo testo è utile per eseguire il task in modo più preciso"
}
}
~~~

#### scheduler:delete_task
Elimina dal sistema il task definito dall'UUID specificato.

##### Argomenti:
* uuid: stringa - L'UUID del task da eseguire. Può essere recuperato ad esempio da "scheduler:tasks_list"

##### Utilizzo (esegui task con uuid "xyz-123"):
~~~json
{
"thoughts": [
"Devo eliminare il task xyz-123",
],
"headline": "Rimozione task dallo scheduler",
"tool_name": "scheduler:delete_task",
"tool_args": {
"uuid": "xyz-123",
}
}
~~~

#### scheduler:create_scheduled_task
Crea un task all'interno del sistema di scheduler con il tipo "scheduled".
Il tipo di task pianificato viene eseguito da una pianificazione cron che devi specificare.

##### Argomenti:
* name: str - Il nome dell'attività, che verrà visualizzato anche durante l'elenco delle attività
* system_prompt: str - Il prompt di sistema da utilizzare durante l'esecuzione dell'attività
* prompt: str - Il prompt effettivo con la definizione dell'attività
* schedule: dict[str,str] - Il dizionario di tutti i valori di cron schedule. Le chiavi sono descrittive: minuto, ora, giorno, mese, giorno della settimana. I valori sono campi della sintassi cron denominati dalle chiavi.
* attachments: list[str] - Qui è possibile aggiungere allegati ai messaggi; sono validi i percorsi del file system e gli URL Internet
* dedicated_context: bool - Se false, l'attività verrà eseguita nel contesto in cui è stata creata. Se true, l'attività avrà un proprio contesto. Se non specificato, si assume il valore false. Per impostazione predefinita, le attività vengono eseguite nel contesto in cui sono state create.

##### Utilizzo:
~~~json
{
"thoughts": [
"Devo creare un'attività pianificata che venga eseguita ogni 20 minuti in una chat separata"
],
"headline": "Creazione di un'attività email ricorrente pianificata tramite cron",
"tool_name": "scheduler:create_scheduled_task",
"tool_args": {
"name": "XXX",
"system_prompt": "Sei uno sviluppatore software",
"prompt": "Invia all'utente un'email con un saluto utilizzando Python e SMTP. L'indirizzo dell'utente è: <EMAIL>",
"attachments": [],
"schedule": {
"minute": "*/20",
"hour": "*",
"day": "*",
"month": "*",
"weekday": "*",
},
"dedicated_context": true
}
}
~~~

#### scheduler:create_adhoc_task
Crea un'attività all'interno del sistema di pianificazione con il tipo "adhoc".
Le attività di tipo adhoc vengono eseguite manualmente dallo strumento "scheduler:run_task" o dall'utente tramite l'interfaccia utente.

##### Argomenti:
* name: str - Il nome dell'attività, che verrà visualizzato anche nell'elenco delle attività
* system_prompt: str - Il prompt di sistema da utilizzare durante l'esecuzione dell'attività
* prompt: str - Il prompt effettivo con la definizione dell'attività
* attachments: list[str] - Qui è possibile aggiungere allegati ai messaggi; sono validi i percorsi del file system e gli URL Internet
* dedicated_context: bool - Se false, l'attività verrà eseguita nel contesto in cui è stata creata. Se true, l'attività avrà un proprio contesto. Se non specificato, verrà assunto il valore false. Per impostazione predefinita, le attività vengono eseguite nel contesto in cui sono state create.

##### Utilizzo:
~~~json
{
"thoughts": [
"Devo creare un'attività ad hoc che possa essere eseguita manualmente quando necessario"
],
"headline": "Creazione di un'attività email su richiesta",
"tool_name": "scheduler:create_adhoc_task",
"tool_args": {
"name": "XXX",
"system_prompt": "Sei uno sviluppatore software",
"prompt": "Invia all'utente un'email con un saluto utilizzando Python e SMTP. L'indirizzo dell'utente è: <EMAIL>",
"attachments": [],
"dedicated_context": false
}
}
~~~

#### scheduler:create_planned_task
Crea un'attività all'interno del sistema di pianificazione con il tipo "pianificato". Il tipo di attività pianificata viene eseguito da un piano fisso, ovvero un elenco di date e ore che è necessario fornire.

##### Argomenti:
* name: str - Il nome dell'attività, che verrà visualizzato anche durante l'elenco delle attività
* system_prompt: str - Il prompt di sistema da utilizzare durante l'esecuzione dell'attività
* prompt: str - Il prompt effettivo con la definizione dell'attività
* plan: list(iso datetime string) - L'elenco di tutti i timestamp di esecuzione. Le date devono essere nel formato iso strftime a 24 ore (!): "%Y-%m-%dT%H:%M:%S"
* attachments: list[str] - Qui è possibile aggiungere allegati ai messaggi; sono validi i percorsi del file system e gli URL Internet
* dedicated_context: bool - Se false, l'attività verrà eseguita nel contesto in cui è stata creata. Se true, l'attività avrà un proprio contesto. Se non specificato, si assume che sia false. Per impostazione predefinita, le attività vengono eseguite nel contesto in cui sono state create.

##### Utilizzo:
~~~json
{
"thoughts": [
"Devo creare un'attività pianificata da eseguire domani alle 18:25",
"Oggi è il 29/04/2025 secondo il prompt di sistema"
],
"headline": "Creazione di un'attività pianificata per una data e ora specifica",
"tool_name": "scheduler:create_planned_task",
"tool_args": {
"name": "XXX",
"system_prompt": "Sei uno sviluppatore software",
"prompt": "Invia all'utente un'e-mail con un saluto utilizzando Python e SMTP. L'indirizzo dell'utente è: <EMAIL>",
"attachments": [],
"plan": ["29/04/2025T18:25:00"],
"dedicated_context": false
}
}
~~~

#### scheduler:wait_for_task
Attendi il completamento di un'attività di scheduler identificata dall'argomento uuid e restituisci il risultato dell'ultima esecuzione dell'attività.
Attenzione: puoi attendere solo le attività in esecuzione in un contesto di chat diverso (dedicato). Le attività con dedicated_context=False non possono essere attese.

##### Argomenti:
* uuid: stringa - L'uuid dell'attività da attendere. Può essere recuperato ad esempio da "scheduler:tasks_list"

##### Utilizzo (attendi l'attività con UUID "xyz-123"):
~~~json
{
"thoughts": [
"Ho bisogno del risultato più aggiornato dell'attività xyz-123",
],
"headline": "In attesa del completamento e dei risultati dell'attività",
"tool_name": "scheduler:wait_for_task",
"tool_args": {
"uuid": "xyz-123",
}
}
~~~
