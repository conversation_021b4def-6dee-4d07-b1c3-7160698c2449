### document_query:
Questo strumento può essere utilizzato per leggere o analizzare documenti remoti e locali.
Può essere utilizzato per:
* Ottenere il contenuto testuale di una pagina web o di un documento remoto
* Ottenere il contenuto testuale di un documento locale
* Rispondere a query su una pagina web, un documento remoto o locale
Per impostazione predefinita, quando l'argomento "queries" è vuoto, questo strumento restituisce il contenuto testuale del documento recuperato tramite OCR.
Inoltre, è possibile passare un elenco di "queries": in questo caso, lo strumento restituisce le risposte a tutte le query passate relative al documento.
!!! Questo è un lettore di documenti universale e uno strumento di query
!!! Formati di documento supportati: HTML, PDF, documenti Office (Word, Excel, PowerPoint), file di testo e molti altri.

#### Argomenti:
* "document" (stringa): l'indirizzo web o il percorso locale del documento in questione. I documenti web necessitano del prefisso di protocollo "http://" o "https://". Per i file locali, il prefisso di protocollo "file:" è facoltativo. I file locali DEVONO essere passati con il percorso completo del file system. * "queries" (facoltativo, list[str]): qui puoi passare una o più query a cui rispondere (utilizzando e/o riguardo al documento).

#### Esempio di utilizzo 1:
##### Richiesta:
```json
{
"thoughts": [
"...",
],
"headline": "Lettura del contenuto del documento web",
"tool_name": "document_query",
"tool_args": {
"document": "https://...somexample",
}
}
```
##### Risposta:
```plaintext
... Ecco l'intero contenuto del documento web richiesto...
```

#### Esempio di utilizzo 2:
##### Richiesta:
```json
{
"thoughts": [
"...",
],
"headline": "Analisi del documento per rispondere a domande specifiche",
"tool_name": "document_query",
"tool_args": {
"document": "https://...somexample",
"queries": [
"Qual è l'argomento?",
"Chi è il pubblico?"
]
}
}
```
##### Risposta:
```testo normale
# Qual è l'argomento?
... Descrizione dell'argomento del documento ...

# Chi è il pubblico?
... L'elenco dei destinatari del documento con brevi descrizioni ...
```