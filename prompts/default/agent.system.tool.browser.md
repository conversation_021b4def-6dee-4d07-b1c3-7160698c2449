### browser_agent:

L'agente subordinato controlla il browser del drammaturgo
L'argomento del messaggio parla con l'agente, fornisce istruzioni chiare, credenziali basate sull'attività
L'argomento di reset genera un nuovo agente
Non resettare se si itera
Sii preciso e descrittivo, ad esempio: apri l'accesso a Google e termina l'attività, accedi usando ... e termina l'attività
Quando esegui il follow-up, inizia: considera le pagine aperte
Non usare la frase attendi istruzioni, usa termina l'attività
Download predefinito in /a0/tmp/downloads

usage:
```json
{
"thoughts": ["Devo accedere a..."],
"headline": "Apertura di una nuova sessione del browser per l'accesso",
"tool_name": "browser_agent",
"tool_args": {
"message": "Apri e accedi a...",
"reset": "true"
}
}
```

```json
{
"thoughts": ["Devo accedere a..."],
"headline": "Continuare con la sessione del browser esistente",
"tool_name": "browser_agent",
"tool_args": {
"message": "Considerando le pagine aperte, clicca...",
"reset": "false"
}
}
```