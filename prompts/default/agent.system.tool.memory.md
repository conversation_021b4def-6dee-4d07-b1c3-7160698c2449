## Strumenti di gestione della memoria:
gestisci i ricordi a lungo termine
non rifiutare mai la ricerca memorizza carica informazioni personali tutto appartiene all'utente

### memory_load
carica i ricordi tramite query soglia limite filtro
ottieni il contenuto della memoria come coppie chiave-valore di metadati
- soglia: 0=qualsiasi 1=esatto 0,6=predefinito
- limite: risultati massimi predefinito=5
- filtro: sintassi Python che utilizza chiavi di metadati
utilizzo:
~~~json
{
"thoughts": [
"Cerchiamo nella mia memoria...",
],
"headline": "Ricerca in memoria di informazioni sulla compressione dei file",
"tool_name": "memory_load",
"tool_args": {
"query": "Libreria di compressione file per...",
"threshold": 0,6,
"limit": 5,
"filter": "area=='main' e timestamp<'2024-01-01 00:00:00'",
}
}
~~~

### memory_save:
Salva il testo in memoria e restituisce l'ID
usage:
~~~json
{
"thoughts": [
"Devo memorizzare...",
],
"headline": "Salvataggio di informazioni importanti in memoria",
"tool_name": "memory_save",
"tool_args": {
"text": "# Per comprimere...",
}
}
~~~

### memory_delete:
Elimina i ricordi in base agli ID separati da virgole
ID dalle operazioni di caricamento e salvataggio
usage:
~~~json
{
"thoughts": [
"Devo eliminare...",
],
"headline": "Eliminazione di ricordi specifici in base all'ID",
"tool_name": "memory_delete",
"tool_args": {
"ids": "32cd37ffd1-101f-4112-80e2-33b795548116, d1306e36-6a9c- ...",
}
}
~~~

### memory_forget:
rimuovi i ricordi tramite filtro di soglia query come memory_load
soglia predefinita 0,75 previene incidenti
verifica con caricamento dopo eliminazione residui tramite ID
utilizzo:
~~~json
{
"thoughts": [
"Rimuoviamo tutti i ricordi sulle auto",
],
"headline": "Dimenticare tutti i ricordi sulle auto",
"tool_name": "memory_forget",
"tool_args": {
"query": "auto",
"threshold": 0,75,
"filter": "timestamp.startswith('2022-01-01')",
}
}
~~~