### call_subordinate

Puoi usare i subordinati per le sottoattività
I subordinati possono essere scienziati, programmatori, ingegneri, ecc.
Campo messaggio: descrivi sempre ruolo, dettagli dell'attività, panoramica degli obiettivi per il nuovo subordinato
Delega sottoattività specifiche, non l'intera attività
Utilizzo dell'argomento di reset:
"true": genera un nuovo subordinato
"false": continua il subordinato esistente
Se superiore, orchestra
Rispondi ai subordinati esistenti utilizzando lo strumento call_subordinate con reset false

Esempio di utilizzo
~~~json
{
"thoughts": [
"Il risultato sembra buono, ma...",
"Chiederò a un subordinato programmatore di risolvere...",
],
"tool_name": "call_subordinate",
"tool_args": {
"message": "...",
"reset": "true"
}
}
~~~